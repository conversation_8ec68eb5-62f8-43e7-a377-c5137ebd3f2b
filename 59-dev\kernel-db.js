"use strict";

const kernelDB = async (sql, params) => {

  const mysql = require('mysql2/promise');

  console.log('enter kernel-db.js >>>>>>>>>>>>>>>>>>>> ');
  console.log('SQL:', sql);
  console.log('params:', params);
  console.log('exit  kernel-db.js <<<<<<<<<<<<<<<<<<<< ');
  
  // create the connection
  
  // config local
  
  const connection = await mysql.createConnection({
    user: 'root',
    host: '127.0.0.1',
    password: 'root', // locahost
    // password: 'LT2Mwfz2DmYqFMHad9XM', // server
    database: 'akademylux-dev'
  });

  
  // query database
  const [rows, fields] = await connection.execute(sql, params);

  // console.log(rows);

  await connection.end();
  return rows;
}

module.exports = kernelDB;

