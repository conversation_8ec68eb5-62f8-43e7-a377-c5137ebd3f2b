const nodemailer = require("nodemailer");

let transporter = nodemailer.createTransport({
  host: "smtp.mail.ru",
  port: 465,
  secure: true, // upgrade later with STARTTLS
  auth: {
    user: "watermeter-info",
    pass: "1rGHVneQBs77eA0r6Pjf"
    // pass: "y011235813"
  }
});

// verify connection configuration
transporter.verify(function (error, success) {
  if (error) {
    console.log(error);
  } else {
    console.log("Server is ready to take our messages");
  }
});

const receiver = "<EMAIL>";
// const receiver = "<EMAIL>";
// const receiver = "<EMAIL>";
// const receiver = "<EMAIL>";
// const receiver = "<EMAIL>";
// bcc: "<EMAIL>",

const message = {
  from: "Счетчик воды v0.1 <<EMAIL>>",
  to: receiver,
  bcc: "<EMAIL>",
  subject: "Message title",
  text: "Plaintext version of the message",
  html: "<p>HTML version of the message</p>"
};

const cb = (err, info) => {
  console.log(err.response);
  console.log(info);
};

transporter.sendMail(message, cb);
