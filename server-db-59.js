"use strict";

const express = require("express");
const app = express();

// TODO сделать так чтобы порт определялся из env
const PORT = 51559;
const TokenKPPCars = '72e8882e-bc90-4fab-82a6-8dad73f435fa';
const TokenKPPPeople = 'Ada3b2fb-6222-42f4-8905-085a99ffcd42';

// https://stackoverflow.com/a/33419220
var fs = require("fs");
var logger = fs.createWriteStream("log.txt", {
  flags: "a" // 'a' means appending (old data will be preserved)
});

let global = {user:{}}; // сюда положим объект текущего юзера, найденного по токену

app.use(express.json());

app.use(setHeaders); // ставим все хедеры как application/json
app.use(headerCheck); // проверяем ошибки в Headers
app.use(bodyParamsCheck); // проверяем ошибки в body запроса

// middlware - обработчик ошибок, последний в очереди мидлварей
// https://www.youtube.com/watch?v=UVAMha41dwo
app.use((error, req, res, next) => {
  console.log("********************* app.use((error, req, res, next)"); // времлог
  const status = error.status || 500;
  const errObj = {
    state: status,
    error: error.message
  };
  res.status(status).json(errObj).end();
  log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
});

// по умолчанию любой отправляемый ответ будет application/json
function setHeaders(req, res, next) {
  res.setHeader("Content-Type", "application/json");
  next();
}

// проверяем обязательные поля в headers
async function headerCheck(req, res, next) {

  console.log(new Date());
  console.log("==== REQUEST HEADERS");
  console.dir(req.headers);
  console.log("==== BODY");
  console.dir(req.body, {depth: null});

  console.log("********************* function headerCheck(req, res, next) - START"); // времлог
  let kernelResponse;

  // проверяем наличие токена авторизации на всех запросах кроме 2 методов
  if (!['getVerificationCode', 'verifyCode'].includes(req.body.method)) {
  
    if (!req.headers.authorization) {
      const error = new Error("Missing parameter 'authorization' in the Header");
      error.status = 401;
      const errObj = {
        state: error.status,
        error: error.message
      };
      res.status(error.status).json(errObj).end();
      log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
      return;
    };
    

    // проверить наличие 
    kernelResponse = await require("./module-methods.js").checkUserSession(req.headers.authorization.split(" ")[1]);

    console.log('Получен ответ от module-methods.checkUserSession: ', kernelResponse);

    if (kernelResponse.error) {
      const error = new Error(kernelResponse.error.message);
      error.status = 401;
      const errObj = {
        state: error.status,
        error: error.message
      };
      res.status(error.status).json(errObj).end();
      console.log("Ошибка");
      console.log(errObj);
      return;
    };
    global.user = kernelResponse;
  }

  if (!req.headers.deviceid) {
    const error = new Error("Missing parameter 'deviceid' in the Header");
    error.status = 422;
    // перенес сюда этот блок из catch(error)
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    // конец блока из catch(error)
    return;
    // next(error);
  }
  console.log("********************* function headerCheck(req, res, next) - OK"); // времлог
  next();
}

// проверяем обязательные поля в body
function bodyParamsCheck(req, res, next) {
  console.log("********************* function bodyParamsCheck(req, res, next) - START"); // времлог
  if (req.body.apikey !== "MqVaGfk*97nVBy_C") {
    const error = new Error("Incorrect apikey");
    error.status = 401;
    // перенес сюда этот блок из catch(error)
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    return;
    // next(error);
  }
  if (!req.body.data) {
    const error = new Error("No 'data' property");
    error.status = 422;
    // перенес сюда этот блок из catch(error)
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    return;
    // next(error);
  }

  console.log("********************* function bodyParamsCheck(req, res, next) - OK"); // времлог
  next();
}

function log(str, req) {
  const d = new Date();
  const nowDate = d.getFullYear() + "/" + d.getMonth() + "/" + d.getDate();
  const nowTime = d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
  const now = nowDate + " " + nowTime;
  // const ip = req.ip.toString().replace("::ffff:", ""); // https://stackoverflow.com/a/54263403
  // logger.write("[" + ip + "] " + now + "\r\n" + str + "\r\n\r\n");
}

// универсальный обработчик респонса всех хендлеров в блоке switch-case
function handleResponse(handlerResponse, req, res, method) {
  if (handlerResponse.errorMsg) {
    const responseJSON = { state: 400, error: handlerResponse.errorMsg, method: method };
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(responseJSON), req);
    res.status(400).json(responseJSON).end();
  } else {
    const responseJSON = { state: 200, method: method };
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(responseJSON), req);
    res.status(200).json(responseJSON).end();
  }
}

app.post("/api/v1", async (req, res) => {
  try {
    // TODO доделать обработку runtime-ошибок внутри app.post чтобы stack-trace c путями не вываливался клиенту
    // TODO тк try-catch не помогает
    // if (err.message) return false;

    const body = req.body;
    const data = body.data;
    let response = {};
    let result = {};
    let kernelResponse;

    switch (body.method) {
      case "test.api":
        console.log("********************* case test.api:"); // времлог
        // тест: возвращаем зеркально джсон body запроса
        response = { state: 200, method: body.method };
        res.status(200).json(response).end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "verifyEmail":
        console.log("********************* case verifyEmail:"); // времлог

        // проверка поля clientMail, если falsy кинуть 400
        if (!req.body.data.clientEmail) {
          const error = new Error("Отсутствие емейла клиента в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок

        result = await require("./sendVerificationCode.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendMeterValues":
        console.log("********************* case sendMeterValues:"); // времлог
        console.log("global.user:", global.user);

        // проверка прошла, основной блок
        result = await require("./module-methods.js").writeMeterValues(body.data.meters, global.user.id);
        res
          .status(200)
          .json({ state: 200, sendMeterValues: "OK", ... {data:result} })
          .end();

        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendOrderSpec":
        console.log("********************* case sendOrderSpec:");

        // проверка поля ucMail, если falsy кинуть 400
        if (!req.body.data.ucEmail) {
          const error = new Error("Отсутствие емейла УК в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendOrderSpec.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendOrderPass":
        console.log("********************* case sendOrderPass:");
        // проверка токена
        // if (!req.headers.authorization) {
        //   const error = new Error("Неверный токен авторизации");
        //   error.status = 401;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };
        
        // if (req.headers.authorization.split(" ")[1] !== '123456789asdfghjkl' ) {
        //   const error = new Error("Неверный токен авторизации");
        //   error.status = 401;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };
        
        // // проверка поля
        // if (!req.body.data) {
        //   const error = new Error("Отсутствие поля data");
        //   error.status = 422;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };

        // проверка прошла, основной блок

        // const passAddInfo = {
        //     "id": "543",
        //     "dateCreated": "2022-07-01 00:12:12",
        //     "status": "open"
        // }
        
        kernelResponse = await require("./module-methods.js").createPass(req.body.data, global.user.id);

        console.log('Получен ответ от module-methods.createPass: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };
        res
          .status(200)
          .json({ ...{ state: 200 }, ...{ data: { ...kernelResponse}}})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      case "sendMessage":
        console.log("********************* case sendMessage:");

        // проверка полей
        if (!req.body.data.ucEmail) {
          const error = new Error("Отсутствие емейла УК в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendMessage.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "updateDeviceToken":
        console.log("********************* case updateDeviceToken:");
        console.log("global.user:", global.user);

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.token) {
          const error = new Error("Отсутствие data.token");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        kernelResponse = await require("./module-methods.js").updatePushToken(
          {
            "deviceid": req.headers.deviceid,
            "pushtoken": body.data.token
          }
        );

        console.log('Получен ответ от module-methods.updatePushToken: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          // .json({ ...{ state: 200 }})
          .json({ ...{ state: 200 }, ...kernelResponse})
          .end();

        // проверка прошла, основной блок
        // result = await require("./updateDeviceToken.js")(body.data);
        // res
        //   .status(200)
        //   .json({ ...{ state: 200 }, ...result })
        //   .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
          
      case "sendPushToAll":
        console.log("********************* case sendPushToAll:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.message || !req.body.data.title) {
          const error = new Error("Отсутствие message или title");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        // result = await require("./sendPushToAll.js")(body.data); // временно вырубил мало ли что
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "getNews":
        console.log("********************* case getNews:");

        // проверка полей кинуть 400
        // if (!req.body.data.message || !req.body.data.title) {
        //   const error = new Error("Отсутствие message или title");
        //   error.status = 400;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };

        // проверка прошла, основной блок
        result = await require("./getNews.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "getNewsArticle":
        console.log("********************* case getNewsArticle:");

        // проверка поля
        if (!req.body.data || !req.body.data.id) {
          const error = new Error("Отсутствие data или id");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./getNewsArticle.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
  
      case "getVerificationCode":
        console.log("********************* case getVerificationCode:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.tel) {
          const error = new Error("Отсутсвует или пустое поле data.tel");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // 
        kernelResponse = await require("./module-methods.js").getVerificationCode(req.headers, req.body.data.tel, req.body.data.mode);

        console.log('Получен ответ от module-methods.getVerificationCode: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        // result = await require("./sendPushToAll.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
    
      case "verifyCode":
        console.log("********************* case verifyCode:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.tel  || !req.body.data.code) {
          const error = new Error("Отсутсвует или пустое поле data.tel или data.code");
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        kernelResponse = await require("./module-methods.js").confirmVerificationCode(req.headers, req.body.data);

        console.log('Получен ответ от module-methods.getVerificationCode: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        res
          .status(200)
          .json({ ...{ state: 200 }, ...kernelResponse })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "getUserData":
        console.log("********************* case getUserData:");
        console.log("global.user: ", global.user);

        kernelResponse = await require("./module-methods.js").getUserData(global.user.id);

        console.log('Получен ответ от module-methods.getUserData: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          .end();
      break;

      case "updateUserData":
        console.log("********************* case updateUserData:");
        console.log("global.user: ", global.user);

        // kernelResponse = await require("./module-methods.js").updateUserData(req.body.data);
        kernelResponse = await require("./module-methods.js").updateUserData(global.user.id, req.body.data);

        console.log('Получен ответ от module-methods.updateUserData: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };
        
        // если все ок - запроси и верни полную модель как в getUserData
        kernelResponse = await require("./module-methods.js").getUserData(global.user.id);
        console.log('Получен ответ от module-methods.getUserData: ', kernelResponse);

        // проверка прошла, основной блок
        // const getUserData022 = require("./getUserDataOnly.json");
        res
          .status(200)
          .json({ ...{ state: 200 }, ... {data: {user:kernelResponse} }})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // получение списка заявок
      case "getPassList":
        console.log("********************* case getPassList:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").getPassList(global.user.id, global.user.tel);
        // kernelResponse = await require("./module-methods.js").getPassList(global.user.id, 'KPPPeople');
        // kernelResponse = await require("./module-methods.js").getPassList(global.user.id, 'KPPCars');

        console.log('Получен ответ от module-methods.getPassList: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: { "passList": kernelResponse }}})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // получение списка заявок
      case "getPassListKPP":
        console.log("********************* case getPassListKPP:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").getPassList();

        console.log('Получен ответ от module-methods.getPassListKPP: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{"passList": kernelResponse }})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // обновление статуса заявки
      case "updatePassStatus":
        console.log("********************* case updatePassStatus:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").updatePassStatus(body.data);

        console.log('Получен ответ от module-methods.updatePassStatus: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // получение массива счетчиков по айди юзера
      case "getMeters":
        console.log("********************* case getMeters:");
        console.log("global.user:", global.user);

        // проверка полей, если falsy кинуть 400
        // if (!req.body.data.accountID) {
        //   const error = new Error("Missing data.accountID");
        //   error.status = 422;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };

        // kernelResponse = await require("./module-methods.js").getMeters(body.data.accountID);
        kernelResponse = await require("./module-methods.js").getMeters(global.user.id);

        console.log('Получен ответ от module-methods.getMeters: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: { "userID": global.user.id, "accounts": kernelResponse }}})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // добавление машины к пользователю
      case "addMyCar":
        console.log("********************* case addMyCar:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").addMyCar(body.data, global.user.id);

        console.log('Получен ответ от module-methods.updatePassStatus: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // получение списка автомобилей пользователя
      case "getMyCars":
        console.log("********************* case getMyCars:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").getMyCars(global.user.id);

        console.log('Получен ответ от module-methods.getMyCars: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // редактирование машины
      case "updateMyCar":
        console.log("********************* case updateMyCar:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").updateMyCar(body.data, global.user.id);

        console.log('Получен ответ от module-methods.updateMyCar: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // редактирование машины
      case "deleteMyCar":
        console.log("********************* case deleteMyCar:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").deleteMyCar(body.data, global.user.id);

        console.log('Получен ответ от module-methods.deleteMyCar: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // получение списка аккаунтов пользователя
      case "getMyAccounts":
        console.log("********************* case getMyAccounts:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").getMyAccounts(global.user.id);

        console.log('Получен ответ от module-methods.getMyAccounts: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // TODO MY users
      // TODO MY users
      // TODO MY users
      // TODO MY users
      // TODO MY users
      // TODO MY users
      // TODO MY users

      // добавление моего пользователя
      case "addMyUser":
        console.log("********************* case addMyUser:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").addMyUser(body.data, global.user.id);

        console.log('Получен ответ от module-methods.updatePassStatus: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // получение списка моих пользователей
      case "getMyUsers":
        console.log("********************* case getMyUsers:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").getMyUsers(global.user.id);

        console.log('Получен ответ от module-methods.getMyUsers: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // редактирование моего пользователя
      case "updateMyUser":
        console.log("********************* case updateMyUser:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").updateMyUser(body.data, global.user.id);

        console.log('Получен ответ от module-methods.updateMyUser: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      // удаление моего пользователя
      case "deleteMyUser":
        console.log("********************* case deleteMyUser:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").deleteMyUser(body.data, global.user.id);

        console.log('Получен ответ от module-methods.deleteMyUser: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;


      // логаут
      case "logout":
        console.log("********************* case logout:");
        console.log("global.user:", global.user);

        kernelResponse = await require("./module-methods.js").logout(global.user.authtoken, global.user.id);

        console.log('Получен ответ от module-methods.logout: ', kernelResponse);

        if (kernelResponse.error) {
          const error = new Error(kernelResponse.error.message);
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

        res
          .status(200)
          .json({ ...{ state: 200 }, ...{data: kernelResponse}})
          // .json({ ...{ state: 200 }, ...kernelResponse})
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
      


      default:
        console.log("********************* case default:");
        response = { state: 422, error: "Метод API не распознан" };
        res.status(422).json(response).end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
    }
  } catch (error) {
    console.log("********************* catch (error)"); // времлог
    console.log(error);
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    return false;
  }
});

app.listen(PORT, err => {
  if (err) {
    return console.log("ОШИБКА");
    // return console.log(err);
  }
  console.log("listening on port", PORT);
});
