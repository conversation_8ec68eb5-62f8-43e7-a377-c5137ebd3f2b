"use strict";

const kernelSMS = async (tel, text) => {

  console.log("inside kernel-sms.js c параметрами: ", tel, text);
  
  const login = '<EMAIL>';
  const apikey = '2hJUNmx2i7b8ec2BRum2BbZwk1';
  const sign = 'SMS Aero';
  const url = `https://${login}:${apikey}@gate.smsaero.ru/v2/sms/send?number=${tel}&text=${text}&sign=${sign}`;
  
  console.log("url для отправки SMS: ", url);

  var axios = require('axios');

  var config = {
    method: 'get',
    url: encodeURI(url), // https://stackoverflow.com/a/48535265/6056120
    // headers: { 
    //   'Cookie': '_csrf=7328d6ec1f983f830c59af222caba361683c396e67931a613eda2b16169c6dd2a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22xnky2am0HOX2teJbjK8plxFPvxE5RPcY%22%3B%7D'
    // }
  };

  axios(config)
  .then(function (response) {
    console.log(JSON.stringify(response.data));
  })
  .catch(function (error) {
    console.log(error);
  });

}


module.exports = kernelSMS;