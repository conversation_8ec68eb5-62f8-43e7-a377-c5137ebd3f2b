CREATE TABLE push_tokens (deviceID char(255) CONSTRAINT firstkey PRIMARY KEY, token varchar(255) NOT NULL);


ALTER TABLE push_tokens ALTER COLUMN deviceID TYPE varchar (255);



/* Adding news to db */

CREATE TABLE news (id serial primary key, timestamp varchar(32), title varchar(256), excerpt varchar(1024), content text);

INSERT INTO news (timestamp, title, excerpt, content) values (`11.07.2022`,`ОТКЛЮЧЕНИЕ ЭЛЕКТРОСНАБЖЕНИЯ В КВАРТИРАХ 9 ПОДЪЕЗДА 12.07.2022Г. С 10:00 ДО 10:30`,`12.07.2022 г. с 10:00 до 10:30 в связи с ремонтными работами, будет отключено электроснабжение в квартирах 9 подъезда`,`Уважаемые собственники!

12.07.2022 г. с 10:00 до 10:30 в связи с ремонтными работами, будет отключено электроснабжение в квартирах 9 подъезда: № 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773

С уважением,
Администрация
ТСН «АКАДЕМИЯ ЛЮКС»`);