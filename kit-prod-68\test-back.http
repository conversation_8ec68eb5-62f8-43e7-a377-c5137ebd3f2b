@host_ip = http://127.0.0.1
# @host_ip = http://**************
# @host_domain = https://apifx.ru
@host_domain = https://app.uk-kit.ru

# основные ендпоинты
@host_full = {{host_ip}}:51569/api/v1
# @host_full = {{host_ip}}:51568/api/v1
@host_full = {{host_domain}}:51579/api/v1
@host_full = {{host_domain}}:51578/api/v1

@host_full_v2 = {{host_ip}}:51569/api/v2
# @host_full_v2 = {{host_ip}}:51568/api/v2
@host_full_v2 = {{host_domain}}:51579/api/v2
@host_full_v2 = {{host_domain}}:51578/api/v2

# acquiring вебхук
# @host_full_v2 = {{host_ip}}:51570/api/v2
# @host_full_v2 = {{host_domain}}:51580/api/v2



# @platform = Android
@platform = iOS
@appversion = 1.4.1
# @appversion = 1.1.0
@content_type = application/json
@useragent = iOS Kit v.1.5.3 PROD



# МГ лок дев
# @auth_token = 68dc6d16-9dc2-4f64-8d4b-4308e7e615b2
# Сибрин лок дев
# @auth_token = f775c15c-5c93-43d2-a880-38197187a9d3
# Сиротина лок дев
# @auth_token = fe94cb90-064d-4527-8613-f05615358f5b
# Юлия Фоменко лок дев
# @auth_token = 80a5251e-3b86-480f-bb92-e92db63749f0
# Я лок дев
# @auth_token = 5977bda9-6308-43ee-80f3-2a7050a6c2e3
# 70000000000 лок дев
# @auth_token = d31c7249-fdb3-4237-8279-c85d63eb9fa1
# 71000000000 лок дев
# @auth_token = df105c33-4a74-4b5d-afc5-c63358e86094
# 71000000001 лок дев
# @auth_token = 6e688a89-c49b-4ba6-a884-fe867d888a32
# 71000000002 лок дев
# @auth_token = c8022cc8-f845-4a81-8737-f005c22f9416
# 71000000003 лок дев
# @auth_token = e3b943d7-f209-469e-b2c4-69a7e56f5ac3
# *********** лок дев
# @auth_token = 4beabbcd-bef6-4cb6-bcca-30da808ba742
# 71000000010 лок дев
# @auth_token = ad843f72-1523-48e5-9cb8-8a72c26f5410
# 79161387582 лок дев, Макарова Наталия, ЖК и апартаменты <<<----------------------------------
@auth_token = 7e50f615-f892-443e-88bd-6e1ee88aa4f3
# 79161387582 лок прод, Макарова Наталия, ЖК и апартаменты <<<----------------------------------
# @auth_token = 84c8c62e-091b-408a-9e02-fbfa8cb6735e
# 79037381122 лок прод, Сибгатуллин Рафаэль Ринатович, упр
# @auth_token = 41562641-9c24-4f52-a1a4-a9025e8925d7
# Я уд дев
# @auth_token = 953b04b7-8736-4a22-af2b-dd5bb035bad6
# МГ уд дев
# @auth_token = 47c90924-c9f3-4138-90f4-be2c77d12b5a
# Юлия Фоменко уд дев
# @auth_token = 847d1a3f-0404-4726-bf01-e01812d7e8b3
# Сиротина уд дев
# @auth_token = 45600a0c-84d3-4a76-98f0-60c81524532f
# Захарова Мария Александровна уд дев
# @auth_token = 8d917747-8506-43cf-b8e8-4adc3bf6eca3
# John Doe (ios test acc ***********) дев
# @auth_token = 71988198-b14e-4714-ad30-947977c998eb
# 70000000000 Тестовый юзер Андройд 00 уд дев
# @auth_token = 5849e170-675d-4410-82bc-38ec428ed495
# 70000000005 Тестовый юзер iOS 05 уд дев
# @auth_token = 2f303763-0041-45b0-b422-fc8fb5d7967a
# 71000000000 уд дев
# @auth_token = 992654e7-7955-4eca-8ada-be72033bcaf6
# 71000000001 уд дев
# @auth_token = 12394115-fae6-4715-9d12-b6f1b0e4f02e
# 71000000002 уд дев
# @auth_token = 257f988b-07b2-46c9-9e5d-659d7e0f4686
# 71000000003 уд дев
# @auth_token = 1b4a4fd1-ea0a-4163-b346-76221bc52419
# *********** уд дев
# @auth_token = 2f076ed0-b0ea-41b4-9a6e-e38eb854542a
# 71000000005 уд дев Тестовый юзер iOS №1
# @auth_token = 88795322-6fb8-47d5-bc5c-8f9ad19a1bb8
# 79266196162 уд дев, Буров много лс, проверка долгов
# @auth_token = ce7f57f7-e5fb-4142-a03b-cb9738489e13
# 79684422888 уд дев, Войт много лс, проверка долгов
# @auth_token = 44dde597-fdc8-4623-ac93-e7f9cbdbf8a1
# 79263359579 уд дев, Вирченко Николай, смена фио проверка
# @auth_token = 9ccfc3c6-4edf-4dcd-8bf5-edcf0d12d797
# 79689341967 уд дев, АРМ охраны, пешие+авто
# @auth_token = c0de5aa3-50bd-47e0-a9b6-a3f5fe19a64c
# 79160744477 уд дев, Патрикеева Елена Борисовна зав домом
# @auth_token = 75f12890-f81e-4f20-a5da-48b64dfa5982
# 79161387582 уд дев, Макарова Наталия, ЖК и апартаменты +++++++++++++++++++++++++++++++++++++++++
@auth_token = da97b595-3fad-4c92-a909-5ca63445fdbb
# 79057414361 уд дев, Нефёдова Ирина Александровна, проверка лс
# @auth_token = 0745c555-f50d-40ca-a9e4-9701c1f09288
# 79168426507 уд дев, Калошин Денис Александрович, проверка лс
# @auth_token = ad3fe64e-a8d5-47fd-8594-55bd556f5f96
# 79057373653 уд дев, Зеленцов Денис Сергеевич
# @auth_token = f67dae96-c9f3-4b87-9dd6-3d70f0c36b05
# 79037381122 уд дев, Сибгатуллин Рафаэль Ринатович, управляющий
# @auth_token = 7607e913-d2b4-4694-8dcc-1d5555cd3ee7




# Я прод
# @auth_token = 8e23a2b6-4ca4-4758-8f3d-06c435462237
# МГ прод
# @auth_token = 064efe40-d17f-44e1-8a62-d5ebd9bca1f7
# Юлия Фоменко прод
# @auth_token = a1564cfa-dca4-4248-9458-a42e41658882
# Сиротина прод
# @auth_token = 49e68db1-7b93-4693-8315-82cf2205c8a9
# Сибрин прод
# @auth_token = a2479117-c46a-4be1-bd51-49a214a75c1e
# Сибрина линкед прод
# @auth_token = 18fed028-934c-4cda-b2ee-22c800a3fbb1
# Эйсмонт прод
# @auth_token = 18ea427d-49bf-40a2-a083-016d12a8d0f5
# Буданова прод
# @auth_token = 296ccfa9-eaa3-4560-b4f5-75cb0e531b95
# Ткачук прод
# @auth_token = e55ad13c-6e9c-431c-8583-0dcdceeadb67
# Балакшина прод - белорусский автономер
# @auth_token = f5af030c-3bcb-4644-a8ac-ebee1a391464
# Долганов Андрей прод - линкед купил два мм и стал мейном
# @auth_token = 9a46ae25-1604-4506-9d5f-9a076603c77a
# Антропов Антон прод - пост жалуется на шлагбаумы
# @auth_token = 854a7604-86d2-441b-be1c-0f14d606ff0d
# Рапанова Юотя прод - Julia в фокус-группе
# @auth_token = d6891771-40d9-4dbc-940f-37db407ccf48
# Васильев Евгений Викторович - проверяем после импрорта ЛС
# @auth_token = ed229912-318b-4bf0-8d0d-40d0827d2a66
# Семененко Юлия Эдуардовна - линкед у которого крашится андройд
# @auth_token = 49297a4d-bad3-4766-98c9-8a4178d06b50
# Цапалина Вера Владимировна - мейн который не видел квартиру
# @auth_token = ea30acd7-9b24-40d7-b115-ce7f7ec8e6a6
# Померанец Ася Сергеевна - мейн  проверить что видит
# @auth_token = 395f2a66-a8f4-47e9-8979-36879e1bbc19
# Толоконников Максим Викторович - мейн проверить лимит авто
# @auth_token = ec244021-e477-420b-9288-2bb748a4a8c4
# АРМ КПП Авто прод 79152504090
# @auth_token = 0042c1e1-c154-4621-9232-cb459be5911d
# АРМ КПП Люди прод 79152504094
# @auth_token = 563533b0-9758-4c7c-b2b5-5c08d39d9b2c
# 79266196162 уд прод, Буров много лс, проверка долгов
# @auth_token = 355a5626-30c7-4cb0-b8fa-47db47450937
# @auth_token = d36bdeb8-a6f7-4638-a8ff-8f52750e6797
# 79166636377 кв.819, Халатян Лилит Артуровна, проверка региона ДНР
# @auth_token = 03ac2181-b904-4284-aca1-2f70d1d34c96
# 79684422888 уд прод, Войт много лс, проверка долгов Войт Войт
# @auth_token = 3bf64beb-d8d3-44f9-a2c6-1c692f8842df
# 79125318150 уд прод, Баймуратов Руслан Габбасович, не видит лс кв 414
# @auth_token = e48d666e-a69d-4304-81d8-10a6a32caa86
# 79160744477 уд дев, Патрикеева Елена Борисовна зав домом
# @auth_token = 477ff4a7-92f4-42a4-9b8a-dc33d075324d
# 79037381122 уд прод, Сибгатуллин Рафаэль Ринатович, управляющий
# @auth_token = 41562641-9c24-4f52-a1a4-a9025e8925d7
#79155547187 уд прод, Белова Ольга Андреевна, КИТ менджер
# @auth_token = 567adf8f-c8a9-4404-9956-f8bc095aec1d
# *********** уд прод, ios тестер (прод, лок и уд)
# @auth_token = 51888d69-adeb-46d3-9e09-0889c69fe5df
# @auth_token = 096bdf09-5368-45b7-af14-1c1c249e2912
# 79161387582 уд прод, Макарова Наталия, ЖК и апартаменты <<<----------------------------------
@auth_token = 84c8c62e-091b-408a-9e02-fbfa8cb6735e
# 79853480888 уд прод, Полубоярова Анна Викторовна, проверка лимитов авто, уд прод
# @auth_token = 5cbd8943-49da-400c-bb66-fdb49e54cfaf
# 79265558295 уд прод, Литвинюк Михаил Филиппович, проверка лимитов авто, уд прод
# @auth_token = 500f138d-7dd2-4152-aeb4-8f57071fac50
# 79647050705 уд прод, Пархоменко Евгений Александрович, не видит квитанции и настройки
# @auth_token = d5f5f9b0-705f-43dd-9863-684603d4618a
# 79057414361 уд пров, Нефёдова Ирина Александровна, проверка лс
# @auth_token = 47a85e4d-56cb-470d-83d1-6c31196680d7
# 79168426507 уд прод, Калошин Денис Александрович, проверка лс
# @auth_token = 37b1f9b7-805f-4f74-859f-26946cbade0f
# 79268552145 уд прод, Добронравова Валентина Павловна, Ап 054, проверка лс
# @auth_token = c1566582-0950-468a-a090-6757d0c3378b
# 79851336642 уд прод, Нестерова Екатерина Михайловна, Кв. 713, проверка лс
# @auth_token = c2ef3caa-94bc-41f4-a581-96b9b6ad8618
# 79175699483 уд прод, Онуприенко Наталия Юрьевна, Мм 160/162, проверка лимита авто
# @auth_token = 268ddf94-9185-4e60-9214-4470a29133b9
# 79150314948 уд прод, линкед, не отображается инфа
# @auth_token = 837b8763-56a5-4415-9e81-1511068deaa9
# 79057373653 уд прод, Зеленцов Денис Сергеевич
# @auth_token = f67dae96-c9f3-4b87-9dd6-3d70f0c36b05
# 79262480719 уд прод, Рукавицына Ирина Евгеньевна, проверить два л/с
# @auth_token = 49144b18-ca96-4a4b-8f88-cc19ec20c2d1
# 79264113495 уд прод, Лохматова Светлана Петровна, проверить лимит авто
# @auth_token = 2192e1ea-586f-474b-a977-b5b5907f10f0
# *********** уд прод, Цветкова Тамара Руслановна, проверить л/с
# @auth_token = 1c247536-fd7f-42b4-940a-6e9a5d497382222





# телефоны
# 79262137488 МГ
# 79263120274 Я
# 79162924297 Фоменко Юлия
# 79857680023 Сиротина
# 79851332864 Сибрин
# *********** John Doe (ios test acc)
# 79031298717 Захарова Мария Александровна



@deviceid = 70000000000


### "method": "test.api" -- НЕ РАБОТАЕТ
POST {{host_full_v2}}/acquiring-webhook
# POST {{host_full_v2}}/acquiring-webhook-123
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "TerminalKey": "1697809269047",
    "OrderId": "934b9d26-5396-42c3-a136-d1e302cf3df5",
    "Success": true,
    "Status": "CANCELED",
    "PaymentId": 4797638579,
    "ErrorCode": "0",
    "Amount": 1100,
    "Token": "370573bbd53ca2ff3535e43ff3c1a8ef75d09d525cec2c34ee1f48c440997c36",
    "Data": {
      "chargeFlag": "false",
      "CUSTOMER_KEY": "userId: 2001109",
      "connection_type": "mobile_sdk",
      "device_model": "SM-A346E",
      "сhosen_method": "MirPay",
      "sdk_version": "3.0.2",
      "main_form": "TinkoffPay",
      "software_version": "34",
      "DeviceOS": "Android",
      "REDIRECT": "false",
      "accept": "text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2"
    }
  }
###


### "method": "test.api" -- НЕ РАБОТАЕТ
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "test.api"
  }
###


### "method": "verifyEmail" -- НЕ РАБОТАЕТ
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "verifyEmail",
    "data": {
        "clientAccount": "**********",
        "clientEmail": "<EMAIL>"
    }
  }
###


### "method": "getVerificationCode"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: 1.5.1
  platform: iOS

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "getVerificationCode",
    "data": {
        "tel" : "***********"
        # "tel" : "***********"
    }
  }

###


### "method": "verifyCode" 
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: ***********
  appversion: 1.5.1
  platform: iOS

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "verifyCode",
    "data": {
      "tel" : "***********",
      "code" : "0000"
    }
  }
###


### "method": "getUserData"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: 1.5.1
  platform: iOS
  useragent: {{useragent}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "getUserData",
    "data": {}
  }
###


### "method": "updateUserData"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: 1.5.1
  platform: iOS

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "updateUserData",
    "data": {
      "dpaSignedDate": "2024-08-22 00:12:22"
    }
  }
###


### "method": "getMyAccounts"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.4.1
platform: iOS

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyAccounts",
  "data": {}
}

###


### "method": "getMyAccountsOld"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyAccountsOld",
  "data": {}
}

###


### "method": "getMeters"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMeters",
  "data": {}
}

###


### "method": "getMetersOld"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMetersOld",
  "data": {}
}

###


### "method": "sendMeterValues"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: {{appversion}}
  platform: {{platform}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "sendMeterValues",
    "data": {
      "meters": [
        {
          "accountNumber": "**********",
          "number": "494070",
          "value": "********"
        },
        {
          "accountNumber": "**********",
          "number": "493910",
          "value": "********"
        }
      ]
    }
  }
###


### "method": "getMyUsers"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyUsers",
  "data": {}
}

###


### "method": "addMyUser"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: 1.5.1
  platform: iOS

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "addMyUser",
    "data": {
      "mainUserID" : 2002019,
      "tel" : "********",
      "display_name" : "display123",
      "chosenName" : "123",
      "chosenMiddleName" : "234",
      "chosenSurName" : "3451111",
      "info" : "",
      "type" : "",
      "linkedUserPermissions" : ""
    }
  }
###


### "method": "updateMyUser"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: 1.5.1
  platform: iOS

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "updateMyUser",
    "data": {
      "id": 2200236,
      "mainUserID" : 2002019,
      "tel" : "123451637",
      "display_name" : "display123456123",
      "chosenName" : "12345",
      "chosenMiddleName" : "234",
      "chosenSurName" : "345",
      "info" : "",
      "type" : "",
      "linkedUserPermissions" : ""
    }
  }
###


### "method": "getMyCars"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyCars",
  "data": {}
}

###


### "method": "getPassList"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getPassList",
  "data": {}
}


### "method": "getPassListARM"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getPassListARM",
  "data": {
    "status": "open"
  }
}

###


GET {{host_full_v2}}/dash
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  
}

###


### "method": "sendOrderPass"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "sendOrderPass",
  "data": {
    "userID": 20,
    "type": "auto",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null,
    "description_text": "Петров ААа"
  }
}

###


POST {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "data": {
    "userID": 20,
    "accountID": null,
    "type": "person",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null,
    "status": "open",
    "favorite": false,
    "idCvs": 0,
    "groupIdCvs": 0,
    "dateCreated": "2022-12-27 12:52:48",
    "dateCreatedCvs": null,
    "dateDelivered": "2022-12-27 12:52:48",
    "dateClosed": "2022-12-27 13:08:22",
    "dateClosedCvs": null,
    "dateCanceled": null,
    "dateCanceledCvs": null
  }
}

###


PATCH {{host_full_v2}}/passes/24735
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "favorite": false
}

###


DELETE {{host_full_v2}}/passes/455
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

###


GET {{host_full_v2}}/passes/455
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

###


GET {{host_full_v2}}/test/503
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


###


GET {{host_full_v2}}/global/user
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

###

GET {{host_full_v2}}/global
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


###


GET {{host_full_v2}}/global/acquiring
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


###


GET {{host_full_v2}}/stats
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/meters/2064
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "name": "кухня-2"
}

###


GET {{host_full_v2}}/meters-history/11100000
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invites
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invites/32
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


PATCH {{host_full_v2}}/invites/32
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "roleId": 4,
  "chosenName": "KZKZsdsdsd",
  "chosenMiddleName": "KZKZ",
  "chosenSurName": "KZKZ"
}

###


DELETE {{host_full_v2}}/invites/50
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invite-user-check/71000000005
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


POST {{host_full_v2}}/invites/create
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "tel" : "71000000000"
}

###


POST {{host_full_v2}}/invites
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "tel": "71000000000",
  "roleId" : 3,
  "chosenName" : "New Name 222",
  "chosenMiddleName" : "New Middle name",
  "chosenSurName" : "New Surname"
}

###


PATCH {{host_full_v2}}/invites/20/accept
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/53/reject
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/52/cancel
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/52/exclude
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/roles
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/roles/4
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


PATCH {{host_full_v2}}/roles/212
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


POST {{host_full_v2}}/roles/create
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


POST {{host_full_v2}}/roles
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "data": {
    "userID": 20,
    "accountID": null,
    "type": "person",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null,
    "status": "open",
    "favorite": false,
    "idCvs": 0,
    "groupIdCvs": 0,
    "dateCreated": "2022-12-27 12:52:48",
    "dateCreatedCvs": null,
    "dateDelivered": "2022-12-27 12:52:48",
    "dateClosed": "2022-12-27 13:08:22",
    "dateClosedCvs": null,
    "dateCanceled": null,
    "dateCanceledCvs": null
  }
}

###


DELETE {{host_full_v2}}/roles/11
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/check-permissions?permissionID=321&value=1
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


###



GET {{host_full_v2}}/services
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS


###


GET {{host_full_v2}}/services/5
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS


###


POST {{host_full_v2}}/services
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

  # "accountID" : 1001288, макарова прод
  # "accountID" : 1001294, макарова дев
  # "accountID" : 1002151, рафаэль прод

{
  "accountID" : 1001288,
  # "accountID" : 1001294,
  # "accountID" : 1002151,
  # "serviceCategoryID" : 1265,
  # "serviceCategoryID" : 1266,
  "serviceCategoryID" : 5,
  "description" : "1"
}


###


POST {{host_full_v2}}/services/images
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

{
  # "accountID" : 1001288,
  "accountID" : 1001294,
  "serviceCategoryID" : 4,
  "description" : "Вот теперь смотрим как заявка сразу появляется в 1С и ей присваивается реальном НОМЕР",
  "files": ""
}


###


PATCH {{host_full_v2}}/services/195
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS

{
  "statusID" : 2
}


###


GET {{host_full_v2}}/service-categories
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS


###


GET {{host_full_v2}}/service-categories?id=105
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: 1.5.1
platform: iOS


###


GET {{host_full_v2}}/assets/img/brick-wall.png
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/arm/passes?number=555
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


###


GET {{host_full_v2}}/arm/config
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}


###


GET {{host_full_v2}}/web/users?id=2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/users/2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/accounts?id=2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/accounts/2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/passes?id=2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/passes/2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/cars?id=2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/cars/2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/meters?id=2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/web/meters/2000101
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


POST {{host_full_v2}}/payments/create
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "accountID": 123456
}

###


POST {{host_full_v2}}/payments
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "id" : 42,
  "order_id" : "01j4w21rx99nf2gzkte71dwfgb",
  "payment_amount" : 120000
}

###


PATCH {{host_full_v2}}/payments/01j4w21rx99nf2gzkte71dwfgb
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

{
  "order_id" : "01j4w21rx99nf2gzkte71dwfgb",
  "payment_status" : "CONFIRMED"
}

###


GET {{host_full_v2}}/operations-history
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/test/503
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/global/user
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/stats

content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/global/service-config 
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###

