"use strict";

const sendMeterValues = async userRequest => {
  let userError = {
    messages: []
  };

  // вытащим имя текущего модуля без расширения
  const path = require("path");
  const moduleName = path.basename(__filename, path.extname(__filename));

  // Отправка показаний самому абоненту

  let messageText = "";
  let messageHTML = "";

  userRequest.meters.forEach(meter => {
    messageHTML += `Счетчик: ${meter.name} <br />`;
    messageHTML += `Серийный номер: ${meter.serial} <br />`;
    messageHTML += `Показания: ${meter.value} <br />`;
    messageHTML += `========================= <br /><br />`;
  });

  let requestParams = {
    clientEmail: userRequest.clientEmail,
    messageSubject: "Показания счетчиков воды КЛИЕНТУ, лицевой счет №" + userRequest.clientAccount,
    messageText: messageText,
    messageHTML: messageHTML
  };

  let kernelResponse = await require("./kernel.js")(requestParams, userError, "");
  if (!kernelResponse) return null; // кернел вернул фолси - отдаем null выше по стеку на уровень БЛ
  
  
  // Отправка показаний в УК

  messageText = "";
  messageHTML = "";
  
  userRequest.meters.forEach(meter => {
    messageHTML += `Счетчик: ${meter.name} <br />`;
    messageHTML += `Серийный номер: ${meter.serial} <br />`;
    messageHTML += `Показания: ${meter.value} <br />`;
    messageHTML += `========================= <br /><br />`;
  });

  requestParams = {
    clientEmail: userRequest.ucEmail,
    messageSubject: "Показания счетчиков воды в УК, лицевой счет №" + userRequest.clientAccount,
    messageText: messageText,
    messageHTML: messageHTML
  };

  kernelResponse = await require("./kernel.js")(requestParams, userError, "");
  if (!kernelResponse) return null; // кернел вернул фолси - отдаем null выше по стеку на уровень БЛ

  return { ...kernelResponse, ...{ sendMeterValues: "OK" } };
};

module.exports = sendMeterValues;