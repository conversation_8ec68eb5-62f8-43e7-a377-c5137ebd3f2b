"use strict";

const sendPushToAll = async userRequest => {
    
  const db = require('./db');

  const config = {
    method: 'post',
    url: 'https://fcm.googleapis.com/fcm/send',
    headers: { 
      'Authorization': 'key=AAAAxAscg_s:APA91bHdHHlHIty0m09URCiiEINErJyh-cW2b2MAaH96LLiIshQTc6wkIwhTHoI78e_CIAa04FDey_nfi6FAeR38VQu8F5YJ9xbrtMtcp6bdsusxIcsFb5G0Y5duV6YJgxPecVYx0leL', 
      'Content-Type': 'application/json'
    },
    // data : data
  };

  const axios = require('axios');

  // запросить 
  const select = await db.query(`SELECT * FROM push_tokens`);
  
  select.rows.forEach(row => {
    console.log(row.token);

    config.data = JSON.stringify({
      "to": row.token,
      "notification": {
        "title": userRequest.title,
        "body": userRequest.message,
      }
    });

    // отправка запроса

    axios(config)
    .then(function (response) {
      console.log(JSON.stringify(response.data));
    })
    .catch(function (error) {
      console.log(error);
    });
  });
};

// sendPushToAll();
module.exports = sendPushToAll;