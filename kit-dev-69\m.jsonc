{
  "water": [
    {
      "id": 2112, // нужен
      "bitrixID": 0,
      "accountID": 1000830,
      "name": "Гостевой холодная",
      "number": "1500814",
      "type": "cold",
      "serialCode": "00-005563",
      "nextCheckDate": "2027-07-24 00:00:00",
      "serviceType": "Холодное водоснабжение",
      "integers": 5,
      "decimals": 3,
      "lastValue": {
        "date": "2023-10-19 00:00:00",
        "value": "448",
        "status": 0,
        "value1c": 448,
        "date1c": "2023-10-19 00:00:00",
        "newValueLimit": 478
      }
    },
    {
      "id": 2113,
      "bitrixID": 0,
      "accountID": 1000830,
      "name": "Основной холодная",
      "number": "1500950",
      "type": "cold",
      "serialCode": "00-005564",
      "nextCheckDate": "2027-07-24 00:00:00",
      "serviceType": "Холодное водоснабжение",
      "integers": 5,
      "decimals": 3,
      "lastValue": {
        "date": "2023-10-19 00:00:00",
        "value": "598",
        "status": 0,
        "value1c": 598,
        "date1c": "2023-10-19 00:00:00",
        "newValueLimit": 628
      }
    }
  ],
  "energy": [
    {
      "id": 2115,
      "bitrixID": 0,
      "accountID": 1000830,
      "name": "Основной горячая",
      "number": "1555876",
      "type": "hot",
      "serialCode": "00-002527",
      "nextCheckDate": "2025-07-24 00:00:00",
      "serviceType": "Холодная вода для ГВС",
      "integers": 5,
      "decimals": 3,
      "lastValue": {
        "date": "2023-10-19 00:00:00",
        "value": "476",
        "status": 0,
        "value1c": 476,
        "date1c": "2023-10-19 00:00:00",
        "newValueLimit": 506
      }
    }
  ],
  "heat": [
    {
      "id": 2115,
      "bitrixID": 0,
      "accountID": 1000830,
      "name": "Основной горячая",
      "number": "1555876",
      "type": "hot",
      "serialCode": "00-002527",
      "nextCheckDate": "2025-07-24 00:00:00",
      "serviceType": "Холодная вода для ГВС",
      "integers": 5,
      "decimals": 3,
      "lastValue": {
        "date": "2023-10-19 00:00:00",
        "value": "476",
        "status": 0,
        "value1c": 476,
        "date1c": "2023-10-19 00:00:00",
        "newValueLimit": 506
      }
    }
  ]
}