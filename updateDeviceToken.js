"use strict";

const updateDeviceToken = async userRequest => {

  // Отправка пуша на указанный токен устройства из видео https://youtu.be/ZQX5cTp4ew0
  // Authorization скопирован из консоли Firebase https://console.firebase.google.com/u/2/project/watermeter-8031d/settings/cloudmessaging/ios:ru.tv.ios.WaterMeter
  // Код для axios скопирован из Postman

  // пейлоад для отправки пуша и токен куда отправлить
  var data = JSON.stringify({
    "to": userRequest.token,
    "notification": {
      "body": `Добро пожаловать в мобильное приложение ТСН "Академия Люкс"!`,
      "title": `Приветствие`
    }
  });
  
  var config = {
    method: 'post',
    url: 'https://fcm.googleapis.com/fcm/send',
    headers: { 
      'Authorization': 'key=AAAAxAscg_s:APA91bHdHHlHIty0m09URCiiEINErJyh-cW2b2MAaH96LLiIshQTc6wkIwhTHoI78e_CIAa04FDey_nfi6FAeR38VQu8F5YJ9xbrtMtcp6bdsusxIcsFb5G0Y5duV6YJgxPecVYx0leL', 
      'Content-Type': 'application/json'
    },
    data : data
  };

  // отправка запроса
  var axios = require('axios');
  
  axios(config)
  .then(function (response) {
    console.log(JSON.stringify(response.data));
  })
  .catch(function (error) {
    console.log(error);
  });

  
  
  // Апдейт токена в базе

  const db = require('./db');
  // запросить 
  const select = await db.query(`SELECT * FROM push_tokens WHERE deviceid=$1`,[userRequest.deviceid]);
  
  // если запись с таким deviceid не найдена - то добавить, иначе обновить
  if (!select.rows.length) {
    await db.query(`INSERT INTO push_tokens (deviceid, token) values ($1, $2) RETURNING *`, [userRequest.deviceid, userRequest.token]);
    return { 'message': 'Устройство добавлено в пул' };
  } else {
    await db.query(`UPDATE push_tokens SET token = $2 WHERE deviceid = $1`, [userRequest.deviceid, userRequest.token]);
    return { 'message': 'Токен устройства обновлен' };
  }
};

module.exports = updateDeviceToken;