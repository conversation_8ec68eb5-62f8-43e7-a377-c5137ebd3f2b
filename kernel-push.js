// Отправка пуша на указанный токен устройства из видео https://youtu.be/ZQX5cTp4ew0
// Authorization скопирован из консоли Firebase https://console.firebase.google.com/u/2/project/watermeter-8031d/settings/cloudmessaging/ios:ru.tv.ios.WaterMeter
// Код для axios скопирован из Postman

"use strict";

const kernelPush = async (userRequest) => {

  // пейлоад для отправки пуша и токен куда отправлить
  var data = JSON.stringify({
    "to": userRequest.pushToken,
    "notification": {
      "body": userRequest.body,
      "title": userRequest.title
    }
  });

  var config = {
    method: 'post',
    url: 'https://fcm.googleapis.com/fcm/send',
    headers: { 
      'Authorization': 'key=AAAAxAscg_s:APA91bHdHHlHIty0m09URCiiEINErJyh-cW2b2MAaH96LLiIshQTc6wkIwhTHoI78e_CIAa04FDey_nfi6FAeR38VQu8F5YJ9xbrtMtcp6bdsusxIcsFb5G0Y5duV6YJgxPecVYx0leL', 
      'Content-Type': 'application/json'
    },
    data : data
  };

  // отправка запроса
  var axios = require('axios');

  axios(config)
  .then(function (response) {
    console.log(JSON.stringify(response.data));
    return response.data;
  })
  .catch(function (error) {
    console.log(JSON.stringify(error));
    return error;
  });

}

module.exports = kernelPush;