# АКАДЕМИЯ
# @cvs_host_full = http://*************:5080

# КИТ старый
# @cvs_host_full = http://***************:5080

# КИТ
@cvs_host_full = http://************:5080

@cvs_content_type = application/json
@cvs_authorization = Basic YWtsdXgxOjAxMTIzNTgxMw==

###

GET {{cvs_host_full}}/groups
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/units
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/cameras
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp?active=0&datefrom=20240708T000000Z
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp/36372
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp?plate=P465OB750
# GET {{cvs_host_full}}/reglp?plate=~120~9~
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}


###

GET {{cvs_host_full}}/lp/latest
# GET {{cvs_host_full}}/lp/current
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}
###

GET {{cvs_host_full}}/lp?plate=M372XX799
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/lp?datefrom=20230916T123854Z
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/lp?idfrom=11562&camera=1
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

POST {{cvs_host_full}}/reglp
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

{
    "plate": "Y000YY22",
    "groupId": 4,
    "externalId": 1004
}

###

POST {{cvs_host_full}}/reglp
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

{
"plate": "m999ee197",
"groupId": 4,
"description": "Учредитель. Полный доступ. Согласовано с упр. Сибгатулинным Р.Р.",
"externalId": 1,
"expireDateTime": "2025-09-20T22:46:00.198Z",
"active": 1,
"temporary": 0
}

###

GET {{cvs_host_full}}/reglp?plate=M311BO50
# GET {{cvs_host_full}}/reglp?description=~2000307~&active=1
# GET {{cvs_host_full}}/reglp?description=~2000307~&groupId=2
# GET {{cvs_host_full}}/reglp?plate=~120~9~
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

PUT {{cvs_host_full}}/reglp
content-type: {{cvs_content_type}}image.png
Authorization: {{cvs_authorization}}

{   
    "id": 13088,
    "plate": "X144BC977",
    "groupId": 3,
    "description": "** ПП: м62(-2), м62(-2), Кв. 197, Кв. 601;  Тел: 79262675850; [2001383]"
}

###

DELETE {{cvs_host_full}}/reglp/19615
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###
