"use strict";

const sendVerificationCode = async userRequest => {
  let userError = {
    messages: []
  };

  // вытащим имя текущего модуля без расширения
  const path = require("path");
  const moduleName = path.basename(__filename, path.extname(__filename));

  // if (!general) userError.messages.push(`Ошибка уровня APIFIX в ${moduleName} : отсутствует значение general`);

  const verificationCode = [..."XXXXXX"].map(ch => Math.round(Math.random() * 9)).join("");

  const referTo = userRequest.type === `UC` ? `Управляющей компании` : `абонента`;

  let messageHTML = `<p>Код верификации ${referTo} в приложении Счетчик Воды: ${verificationCode}</p>`;
  messageHTML += `<br /><br />----------<br /><small>Если вы не знаете про что это письмо, то проигнорируйте его.</small>`;

  const requestParams = {
    clientEmail: userRequest.type === `UC` ? userRequest.ucEmail : userRequest.clientEmail,
    messageSubject: userRequest.type === `UC` ? 
      `Подтверждение email управляющей компании`:
      `Подтверждение email, лицевой счет №${userRequest.clientAccount}`,
    messageText: `Ваш код ${verificationCode}`,
    messageHTML: messageHTML
  };

  let kernelResponse = await require("./kernel.js")(requestParams, userError, "");
  if (!kernelResponse) return null; // кернел вернул фолси - отдаем null выше по стеку на уровень БЛ

  return { ...kernelResponse, ...{ sendVerificationCode: "OK", verificationCode: verificationCode } };
};

module.exports = sendVerificationCode;
