@host_ip = http://**************

@host_full = http://127.0.0.1:51559/api/v1
@host_full = http://127.0.0.1:51558/api/v1
@host_full = http://**************:51559/api/v1
# @host_full = http://**************:51558/api/v1

@host_full_v2 = http://127.0.0.1:51559/api/v2
@host_full_v2 = http://127.0.0.1:51558/api/v2
@host_full_v2 = http://**************:51559/api/v2
# @host_full_v2 = http://**************:51558/api/v2

# @platform = Android
@platform = iOS
@appversion = 1.0.26
# @appversion = 1.1.0
@content_type = application/json
# МГ лок дев
# @auth_token = 68dc6d16-9dc2-4f64-8d4b-4308e7e615b2
# Сибрин лок дев
# @auth_token = f775c15c-5c93-43d2-a880-38197187a9d3
# Сиротина лок дев
# @auth_token = fe94cb90-064d-4527-8613-f05615358f5b
# Юлия Фоменко лок дев
# @auth_token = 80a5251e-3b86-480f-bb92-e92db63749f0
# Я лок дев
# @auth_token = 5977bda9-6308-43ee-80f3-2a7050a6c2e3
# *********** лок дев
@auth_token = c5c475a7-85df-407e-94d7-1f6af7af94bb
# *********** лок дев
# @auth_token = d31c7249-fdb3-4237-8279-c85d63eb9fa1
# 71000000000 лок дев
# @auth_token = 56131792-0557-4197-8b5d-7a0f4e02b1e9
# 71000000001 лок дев
# @auth_token = 6e688a89-c49b-4ba6-a884-fe867d888a32
# 71000000002 лок дев
# @auth_token = c8022cc8-f845-4a81-8737-f005c22f9416
# 71000000003 лок дев
# @auth_token = e3b943d7-f209-469e-b2c4-69a7e56f5ac3
# *********** лок дев
# @auth_token = 4beabbcd-bef6-4cb6-bcca-30da808ba742
# 71000000010 лок дев
# @auth_token = ad843f72-1523-48e5-9cb8-8a72c26f5410
# Я уд дев
# @auth_token = 25a8a5ab-28b5-4cb3-b1d8-6df993cefe5e
# МГ уд дев
# @auth_token = 47c90924-c9f3-4138-90f4-be2c77d12b5a
# Юлия Фоменко уд дев
# @auth_token = 847d1a3f-0404-4726-bf01-e01812d7e8b3
# Сиротина уд дев ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
@auth_token = 385d3288-b965-4329-bb33-4a10a95837af
# Захарова Мария Александровна уд дев
# @auth_token = 8d917747-8506-43cf-b8e8-4adc3bf6eca3
# John Doe (ios test acc 72222222222) дев
# @auth_token = 71988198-b14e-4714-ad30-947977c998eb
# *********** Тестовый юзер Андройд 00 уд дев
# @auth_token = 5849e170-675d-4410-82bc-38ec428ed495
# 70000000005 Тестовый юзер iOS 05 уд дев
# @auth_token = 2f303763-0041-45b0-b422-fc8fb5d7967a
# 71000000000 уд дев
# @auth_token = 27031ffa-fd06-4aaa-aa4c-6336ce7601a1
# 71000000001 уд дев
# @auth_token = 12394115-fae6-4715-9d12-b6f1b0e4f02e
# 71000000002 уд дев
# @auth_token = 257f988b-07b2-46c9-9e5d-659d7e0f4686
# 71000000003 уд дев
# @auth_token = 1b4a4fd1-ea0a-4163-b346-76221bc52419
# *********** уд дев
# @auth_token = 2f076ed0-b0ea-41b4-9a6e-e38eb854542a
# 71000000010 уд дев
# @auth_token = 
# Я прод
# @auth_token = 6b628a7c-fb5b-4691-bbca-ebb5a204c7f9
# МГ прод
# @auth_token = 064efe40-d17f-44e1-8a62-d5ebd9bca1f7
# Юлия Фоменко прод
# @auth_token = a1564cfa-dca4-4248-9458-a42e41658882
# Сиротина прод +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
# @auth_token = 49e68db1-7b93-4693-8315-82cf2205c8a9
# Сибрин прод
# @auth_token = a2479117-c46a-4be1-bd51-49a214a75c1e
# Сибрина линкед прод
# @auth_token = 18fed028-934c-4cda-b2ee-22c800a3fbb1
# Эйсмонт прод
# @auth_token = 18ea427d-49bf-40a2-a083-016d12a8d0f5
# Буданова прод
# @auth_token = 296ccfa9-eaa3-4560-b4f5-75cb0e531b95
# Ткачук прод
# @auth_token = e55ad13c-6e9c-431c-8583-0dcdceeadb67
# Балакшина прод - белорусский автономер
# @auth_token = f5af030c-3bcb-4644-a8ac-ebee1a391464
# Долганов Андрей прод - линкед купил два мм и стал мейном
# @auth_token = 9a46ae25-1604-4506-9d5f-9a076603c77a
# Антропов Антон прод - пост жалуется на шлагбаумы
# @auth_token = 854a7604-86d2-441b-be1c-0f14d606ff0d
# Рапанова Юотя прод - Julia в фокус-группе
# @auth_token = d6891771-40d9-4dbc-940f-37db407ccf48
# Васильев Евгений Викторович - проверяем после импрорта ЛС
# @auth_token = ed229912-318b-4bf0-8d0d-40d0827d2a66
# Семененко Юлия Эдуардовна - линкед у которого крашится андройд
# @auth_token = 49297a4d-bad3-4766-98c9-8a4178d06b50
# Цапалина Вера Владимировна - мейн который не видел квартиру
# @auth_token = ea30acd7-9b24-40d7-b115-ce7f7ec8e6a6
# Померанец Ася Сергеевна - мейн  проверить что видит
# @auth_token = 395f2a66-a8f4-47e9-8979-36879e1bbc19
# Толоконников Максим Викторович - мейн проверить лимит авто
# @auth_token = ec244021-e477-420b-9288-2bb748a4a8c4
# АРМ КПП Авто прод 79152504090
# @auth_token = 0042c1e1-c154-4621-9232-cb459be5911d
# АРМ КПП Люди прод 79152504094
# @auth_token = 563533b0-9758-4c7c-b2b5-5c08d39d9b2c
# прод 79859231250 Давыдов Сергей Владимирович, кв 960
# @auth_token = cb50a0fb-d61f-42b7-8141-3aad079d4c62
# прод 79039610415 Давыдова Анна Владимировна, Мм 406,407
# @auth_token = abf90150-efab-4a75-8a37-d4c92d3b8dc7
# прод 79165314548 Терещук Григорий Владимирович , Кв 313, линкеда стал мейном
# @auth_token = b3d0f824-f9c4-4f39-a7ee-ca313a8d45b6
# прод 79153292324 Бадула Марина Васильевна, Кв 945, Мм 864-867, новый лс
# @auth_token = 54e0b905-37f9-4150-92a0-f8a6540bc344
# прод 79647863333 Костюков Александр Владимирович, Кв 184, нет доступа к лс у мейна
# @auth_token = 5bc354f4-3ab8-4931-884a-dfcbb9baed19
# прод 79031570130 Финк Иван Петрович, Кв 184, не видит ГВС, жалуется через ГИС ЖКХ
# @auth_token = 6330ee3b-5dd4-4085-b4c6-8b124b94ce23
# прод 79995654805 выдает ошибку Couldn't connect to the server
# @auth_token = 77c59474-8f40-47c6-a431-1393b4a57509
# прод 79165261833 линкед, пустой экран
# @auth_token = 4ecb5856-5163-4583-9acb-5a1a1320d0e1
# прод 79166902971 7я кв, не показывает номера лс и глюки
# @auth_token = 4f6072ed-7ea5-4bb6-96b7-259cf32352b4
# прод 79104995737 Кузнецов Евгений Петрович, доб 2 мм
# @auth_token = 52712fb8-531b-4d24-9ccd-97a0e1014e26
# прод 79257723263 Бочарников Александр Николаевич, доб л/с, пров лимит авто
# @auth_token = 0d9e45fe-4f81-4c67-95e8-acbc14da5a2d
# прод 79268293216 Жуков Андрей Юрьевич, тест счетчиков
# @auth_token = c281cf8a-9328-4213-94cd-ff5403acf9d2




# телефоны
# 79262137488 МГ
# 79263120274 Я
# 79162924297 Фоменко Юлия
# 79857680023 Сиротина
# 79851332864 Сибрин
# 72222222222 John Doe (ios test acc)
# 79031298717 Захарова Мария Александровна



@deviceid = ***********

### "method": "test.api" -- НЕ РАБОТАЕТ
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "test.api"
  }
###


### "method": "verifyEmail" -- НЕ РАБОТАЕТ
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "verifyEmail",
    "data": {
        "clientAccount": "**********",
        "clientEmail": "<EMAIL>"
    }
  }
###


### "method": "getVerificationCode" Я
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "getVerificationCode",
    "data": {
        "tel" : "***********"
    }
  }

###


### "method": "verifyCode" Я
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: ***********
  appversion: {{appversion}}
  platform: {{platform}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "verifyCode",
    "data": {
      "tel" : "***********",
      "code" : "0000"
    }
  }
###


### "method": "getUserData"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: {{appversion}}
  platform: {{platform}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "getUserData",
    "data": {}
  }
###


### "method": "updateUserData"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "updateUserData",
    "data": {
        "dpaSignedDate": "2022-08-22 00:12:22"
    }
  }
###


### "method": "getMyAccounts"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: {{appversion}}
  platform: {{platform}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyAccounts",
  "data": {}
}

###


### "method": "getMyAccountsOld"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyAccountsOld",
  "data": {}
}

###


### "method": "getMeters"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMeters",
  "data": {}
}

###


### "method": "getMetersOld"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMetersOld",
  "data": {}
}

###


### "method": "sendMeterValues"
POST {{host_full}}
  content-type: {{content_type}}
  Authorization: Bearer {{auth_token}}
  deviceid: {{deviceid}}
  appversion: {{appversion}}
  platform: {{platform}}

  {
    "apikey": "MqVaGfk*97nVBy_C",
    "method": "sendMeterValues",
    "data": {
      "meters": [
        {
          "accountNumber": "**********",
          "number": "494070",
          "value": "********"
        },
        {
          "accountNumber": "**********",
          "number": "493910",
          "value": "********"
        }
      ]
    }
  }
###


### "method": "getMyUsers"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyUsers",
  "data": {}
}

###


### "method": "getMyCars"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getMyCars",
  "data": {}
}

###


### "method": "getPassList"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getPassList",
  "data": {}
}


### "method": "getPassListARM"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "getPassListARM",
  "data": {}
}

###


GET {{host_full_v2}}/dash
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}
appversion: {{appversion}}
platform: {{platform}}

###


GET {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  
}

###


### "method": "sendOrderPass"
POST {{host_full}}
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "apikey": "MqVaGfk*97nVBy_C",
  "method": "sendOrderPass",
  "data": {
    "userID": 20,
    "type": "auto",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null
  }
}

###


POST {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "data": {
    "userID": 20,
    "accountID": null,
    "type": "person",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null,
    "status": "open",
    "favorite": false,
    "idCvs": 0,
    "groupIdCvs": 0,
    "dateCreated": "2022-12-27 12:52:48",
    "dateCreatedCvs": null,
    "dateDelivered": "2022-12-27 12:52:48",
    "dateClosed": "2022-12-27 13:08:22",
    "dateClosedCvs": null,
    "dateCanceled": null,
    "dateCanceledCvs": null
  }
}

###


PATCH {{host_full_v2}}/passes/353
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "favorite": true
}

###


DELETE {{host_full_v2}}/passes/455
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/passes
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/passes/455
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/test/503
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/global/user
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/stats
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/meters/2064
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "name": "кухня-2"
}

###


GET {{host_full_v2}}/meters-history/11100000
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invites
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invites/32
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


PATCH {{host_full_v2}}/invites/32
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "roleId": 4,
  "chosenName": "KZKZsdsdsd",
  "chosenMiddleName": "KZKZ",
  "chosenSurName": "KZKZ"
}

###


DELETE {{host_full_v2}}/invites/50
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/invite-user-check/71000000005
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


POST {{host_full_v2}}/invites/create
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "tel" : "71000000000"
}

###


POST {{host_full_v2}}/invites
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "tel": "71000000000",
  "roleId" : 3,
  "chosenName" : "New Name 222",
  "chosenMiddleName" : "New Middle name",
  "chosenSurName" : "New Surname"
}

###


PATCH {{host_full_v2}}/invites/20/accept
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/53/reject
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/52/cancel
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


PATCH {{host_full_v2}}/invites/52/exclude
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/roles
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/roles/4
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


PATCH {{host_full_v2}}/roles/212
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


POST {{host_full_v2}}/roles/create
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


POST {{host_full_v2}}/roles
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "data": {
    "userID": 20,
    "accountID": null,
    "type": "person",
    "courier": true,
    "personAmount": null,
    "autoNumberType": "standard",
    "autoNumber": "Y000YY44",
    "specialNumberFormat": false,
    "autoBrand": null,
    "status": "open",
    "favorite": false,
    "idCvs": 0,
    "groupIdCvs": 0,
    "dateCreated": "2022-12-27 12:52:48",
    "dateCreatedCvs": null,
    "dateDelivered": "2022-12-27 12:52:48",
    "dateClosed": "2022-12-27 13:08:22",
    "dateClosedCvs": null,
    "dateCanceled": null,
    "dateCanceledCvs": null
  }
}

###


DELETE {{host_full_v2}}/roles/11
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/check-permissions?permissionID=321&value=1
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


###


GET {{host_full_v2}}/services
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/services/69
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


POST {{host_full_v2}}/services
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "accountID" : 1200010,
  "serviceCategoryID" : 4,
  "description" : "Бла бла почините дверь close",
  "files": ""
}


###


PATCH {{host_full_v2}}/services/66
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

{
  "statusID" : 5
}


###


GET {{host_full_v2}}/service-categories
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/service-categories?id=100
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}


###


GET {{host_full_v2}}/assets/img/brick-wall.png
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/arm/passes?number=555
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###


GET {{host_full_v2}}/arm/config
content-type: {{content_type}}
Authorization: Bearer {{auth_token}}
deviceid: {{deviceid}}

###