{ 
  "water": 
  {
    "title" : "Водоснабжение",
    "data" : [
      {
        "id": 2112, // нужен, не отображается в приложении
        "accountID": 1000830, // хз, не отображается в приложении
        "name": "Гостевой холодная", // нужен, отображается крупно как заголовок
        "number": "1500814", // нужен, отображается как "Номер счетчика" в фигме, но как "Заводской номер" в андройде, хз как в айосе
        "type": "cold", // нужен, влияет на иконку (синий/красный) и тег (холодная вода/горячая вода)
        "serialCode": "00-005563", // не нужен, не отображается в приложении (проверь в айосе)
        "nextCheckDate": "2027-07-24 00:00:00", // нужен, отображается как "Дата поверки"
        "serviceType": "Холодное водоснабжение", // хз, не отображается в приложении
        "integers": 5, // нужен, логика, вводили для разрядности
        "decimals": 3, // нужен, логика, вводили для разрядности
        "lastValue": {
          "date": "2023-10-19 00:00:00", // нужен, отображается в миникарточке и в полной
          "value": "448", // нужен, отображается в миникарточке и в полной
          "status": 0, // нужен, логика, хотя хз используют ли
          "value1c": 448, // хз
          "date1c": "2023-10-19 00:00:00", // хз
          "newValueLimit": 478 // нужен, логика лимита, модалка
        }
      },
      {
        "id": 2113,
        "accountID": 1000830,
        "name": "Основной холодная",
        "number": "1500950",
        "type": "cold",
        "serialCode": "00-005564",
        "nextCheckDate": "2027-07-24 00:00:00",
        "serviceType": "Холодное водоснабжение",
        "integers": 5,
        "decimals": 3,
        "lastValue": {
          "date": "2023-10-19 00:00:00",
          "value": "598",
          "status": 0,
          "value1c": 598,
          "date1c": "2023-10-19 00:00:00",
          "newValueLimit": 628
        }
      }
    ]
  },

  "energy": 
  {
    "title" : "Электроснабжение",
    "data" : [
      {
        "id": 2115,
        "accountID": 1000830,
        "name": "Основной горячая",
        "number": "1555876",
        "type": "hot",
        "serialCode": "00-002527",
        "nextCheckDate": "2025-07-24 00:00:00",
        "serviceType": "Холодная вода для ГВС",
        "integers": 5,
        "decimals": 3,
        "lastValue": {
          "date": "2023-10-19 00:00:00",
          "value": "476",
          "status": 0,
          "value1c": 476,
          "date1c": "2023-10-19 00:00:00",
          "newValueLimit": 506
        }
      }
    ]
  },

  "heat": 
  {
    "title" : "Отопление",
    "data" : [
      {
        "id": 2115,
        "accountID": 1000830,
        "name": "Основной горячая",
        "number": "1555876",
        "type": "hot",
        "serialCode": "00-002527",
        "nextCheckDate": "2025-07-24 00:00:00",
        "serviceType": "Холодная вода для ГВС",
        "integers": 5,
        "decimals": 3,
        "lastValue": {
          "date": "2023-10-19 00:00:00",
          "value": "476",
          "status": 0,
          "value1c": 476,
          "date1c": "2023-10-19 00:00:00",
          "newValueLimit": 506
        }
      }
    ]
  }
}