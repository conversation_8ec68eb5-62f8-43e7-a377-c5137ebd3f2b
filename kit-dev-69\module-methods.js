// "use strict";

require('dotenv').config();

// const verificationTimeout = 60;

const ENV = process.env;
const verificationTimeout = Number(ENV.SmsVerificationTimeoutDev); // тк в .env все значения - строки
const sendMeterValuesPermission = ENV.sendMeterValuesDev === "true" ? true : false; // флаг активности окна отправки показаний

const formatISOTimeStamp = ISOtimeStamp => {
  // 2022-07-31T21:00:00.000Z ===> "2022-08-01 00:00:00"
  const date = new Date(ISOtimeStamp).toLocaleDateString('sv-SV');
  const time = new Date(ISOtimeStamp).toLocaleTimeString('ru-RU');
  return date + " " + time;
};

const formatDateToBillPeriod = ISOtimeStamp => {
  // 2022-07-31T21:00:00.000Z ===> "Август 2022"
  const months = ['Январь','Февраль','Март','Апрель','Май','Июнь',
  'Июль','Август','Сентябрь','Октябрь','Ноябрь','Декабрь'];
  return months[new Date(ISOtimeStamp).getMonth()] + ' ' + new Date(ISOtimeStamp).getFullYear();
};

const formatDateToBillPeriodNew = monthYearStr => {
  // 8.2022 ===> "Август 2022"
  const months = ['Январь','Февраль','Март','Апрель','Май','Июнь',
  'Июль','Август','Сентябрь','Октябрь','Ноябрь','Декабрь'];
  const monthYear = monthYearStr.split('.');
  return months[monthYear[0]-1] + ' ' + monthYear[1].toString();
};

const formatMysqlDecimalToCur = number => {
  // 3599.24 ===> "3 599,24"
  // https://developer.mozilla.org/ru/docs/Web/JavaScript/Reference/Global_Objects/Number/toLocaleString
  number = number ? number : 0; // если number NULL, то parseInt возвращал NaN
  return Number.parseFloat(number).toLocaleString('ru-RU', { style: 'currency', currency: 'RUB' });
};

const formatRusDateToEuro = rusDate => {
  // "28.02.2023 0:00:00" ===> "2023-02-28 00:00:00"
  const dd = rusDate.substring(0, 10).split('.');
  return `${dd[2]}-${dd[1]}-${dd[0]} 00:00:00`;
};

// TODO Разбить логику работы с автономерами на 3 независимые ф-ии:
// TODO isAutoNumberGost(string):boolean
// TODO translitRuToEn(string):string
// TODO translitEnToRu(string):string

const checkAutoNumber = autoNumber => {
  // проверка автономера на ГОСТ
  console.log(`Проверяем автономер "${autoNumber}" на соответствие ГОСТу`);
  // правила проверки
  // 1. в строке должны быть только буквы русского или лат алфавита из списка
  // TODO 2. маска должна удовлетворять БЦЦЦББЦЦ(Ц)// пока забей
  // RUS: АВЕКМНОРСТУХ
  // LAT: ABEKMHOPCTYX
  
  const rus = "АВЕКМНОРСТУХ";
  const lat = "ABEKMHOPCTYX";
  const digits = "0123456789";

  let translit = '';

  autoNumber = autoNumber.replace(/\s/g, ""); // убираем все пробелы https://stackoverflow.com/a/6623252/6056120

  for (let c of autoNumber.split('')) {
    cU = c.toUpperCase(); // символ переводим в заглавный
    if ((rus + lat + digits).indexOf(cU) === -1) return false; // символ не из набора - выходим
    if (rus.split('').includes(cU)) cU = lat[rus.indexOf(cU)]; // символ кириллический - транслитерируем
    translit += cU; // склеиваем
  };

  return translit;

};

// разница в часах между сейчас и датой date в формате Date()
const time_diff = date => {
  return (Date.now() - date.getTime()) / 3600000; 
};

const accruals_account_number_0172 = [
  {
   "service_name": "Содержание и ремонт жилых помещений",
   "service_dolg": "34158,38 Р"
  },
  {
   "service_name": "Отопление",
   "service_dolg": "57,34 Р"
  },
  {
   "service_name": "Охрана",
   "service_dolg": "3617,37 Р"
  },
  {
   "service_name": "ГВС (ХВС для ГВС)",
   "service_dolg": "10,11 Р"
  },
  {
   "service_name": "Подогрев воды",
   "service_dolg": "28,67 Р"
  },
  {
   "service_name": "Консьерж-услуги",
   "service_dolg": "4402,68 Р"
  },
  {
   "service_name": "Электроэнергия ИПУ",
   "service_dolg": "1461,07 Р"
  },
  {
   "service_name": "Электроэнергия ОДН",
   "service_dolg": "4665,22 Р"
  },
  {
   "service_name": "Вывоз мусора (Sкв.*285руб.\/6мес.)",
   "service_dolg": "17071,05 Р"
  },
  {
   "service_name": "Единоразовое начисление за обустройство помещ.консьержей",
   "service_dolg": "240,03 Р"
  },
  {
   "service_name": "Водоотведение",
   "service_dolg": "190,02 Р"
  },
  {
   "service_name": "ХВС",
   "service_dolg": "186,78 Р"
  },
  {
   "service_name": "Обращение с ТКО",
   "service_dolg": "2917,15 Р"
  },
  {
   "service_name": "ОДПУ",
   "service_dolg": "4894,28 Р"
  },
  {
   "service_name": "ХВС ОДН",
   "service_dolg": "97,38 Р"
  },
  {
   "service_name": "Обращение с ТКО (с мая по сентябрь)",
   "service_dolg": "349,82 Р"
  },
  {
   "service_name": "Водоотведение ОДН",
   "service_dolg": "93,98 Р"
  }
 ];

// Проверка наличия номера телефона в базе
// Генерация кода верификации
exports.getVerificationCode = async (headers, tel, mode) => {
  console.log('inside getVerificationCode');
  console.log('deviceid', headers.deviceid);
  
  // есть ли юзер с таким телефоном в табл users
  let sql, params, result;
  sql = 'SELECT * FROM `users` WHERE `tel` = ?';
  params = [tel];
  result = await require('./kernel-db.js')(sql, params);
  
  // let sqlLinked, paramsLinked, resultLinked;
  // // есть ли юзер с таким телефоном в базе user_my
  // sqlLinked = 'SELECT * FROM `users_my` WHERE `tel` = ?';
  // paramsLinked = [tel];
  // resultLinked = await require('./kernel-db.js')(sqlLinked, paramsLinked);
  
  
  // телефон не найден - выйти с ошибкой
  if (!result.length) {
    sql = 'INSERT INTO verificationsessions_fail (tel,deviceid) VALUES (?,?)';
    params = [tel, headers.deviceid];
    const addVerificationSessionFail = await require('./kernel-db.js')(sql, params);
    return { 
      "error" : 
        {"message": "Данный номер телефона не зарегистрирован. Пожалуйста, обратитесь в офис УК для регистрации."}
      };
  };

  const type = result[0].type;



  // если запрос на смс не с планшета и не с тестового аккаунта
  // if (!['00000000001', '00000000002', '00000000003', '71111111111', '72222222222'].includes(tel)) {
  if (![ENV.TelKppAutoKit, ENV.TelKppPersonKit, ENV.TelKppAutoPersonKit, ENV.TelTesterAndroid, ENV.TelTesterIos].includes(tel)) {
    // узнать был ли запрошен СМС на этот номер последние N секунд
    sql = 'SELECT * FROM `verificationsessions` WHERE `tel` = ? AND TIMESTAMPDIFF(SECOND,ts,CURRENT_TIMESTAMP) < ?';
    params = [tel, verificationTimeout];
    const lastSession = await require('./kernel-db.js')(sql, params);
    if (lastSession.length > 0) {
      return { 
        "error" : 
          {"message": "Не вышел таймаут для повторного запроса СМС"}
        }
    }
  }

  // можно отсылать код верификации, без нулей
  const verificationCode = [..."XXXX"].map(ch => Math.round(Math.random() * 8 + 1)).join("");
  // сохранение сессии верификации в БД с номером тел / deviceid / 
  sql = 'INSERT INTO verificationsessions (tel,vericode,deviceid) VALUES (?,?,?)';
  params = [tel, verificationCode, headers.deviceid];
  const addVerificationSession = await require('./kernel-db.js')(sql, params);
  
  console.log("addVerificationSession: ", addVerificationSession);

  // отправляем СМС с кодом верификации
  const SMSMessage = `${verificationCode} - код подтверждения номера телефона в приложении КИТ-Сервис`;	
  // отправка реального SMS, кроме планшетов, тестовых акков и когда в body запроса data.mode = "test"	
  // if (!['00000000001', '00000000002', '00000000003', '71111111111', '72222222222'].includes(tel) && mode !== "test") { const sendSMS = await require('./kernel-sms.js')(tel, SMSMessage) };
  
  return { ...{ tel: tel, type: type, timer: verificationTimeout} };
};


exports.confirmVerificationCode = async (headers, userRequest) => {
  console.log('inside confirmVerificationCode с параметрами: ', headers, userRequest);
  let sql, params, result;
  
  // при подключении реальных СМС, раскаментить весь блок
  // на всякий случай уточним есть ли активная сессия верификации на этот номер за посл N секунд
  sql = 'SELECT * FROM `verificationsessions` WHERE `tel` = ? AND TIMESTAMPDIFF(SECOND,ts,CURRENT_TIMESTAMP) < ? ORDER BY ts DESC LIMIT 1';
  params = [userRequest.tel, verificationTimeout];
  result = await require('./kernel-db.js')(sql, params);

  // записи не найдены - кидаем ошибку перезапросить код, кроме случая кода 0000
  if (!result.length && userRequest.code !== ENV.SmsCodeDev) {
    return { 
      "error" : 
      {"message": "Сессия устарела или не найден тел, необходимо перезапросить код"}
    }
  }
  
  // сравниваем код верификации
  // console.log('Коды верификации 1) от юзера 2) и последний из базы: ', userRequest.code, result[0].vericode)
  
  // при подключении реальных СМС заменить строку ниже и удалить с 0000
  // либо остаить 0000 при условии что mode = test в запросе
  // if (!(['00000000001', '00000000002', '00000000003', '71111111111', '72222222222'].includes(userRequest.tel)) && userRequest.code !== '0000') {
  if (!([ENV.TelKppAutoKit, ENV.TelKppPersonKit, ENV.TelKppAutoPersonKit, ENV.TelTesterAndroid, ENV.TelTesterIos].includes(userRequest.tel)) && userRequest.code !== ENV.SmsCodeDev ) {
    if (userRequest.code !== result[0].vericode) {
      return { 
        "error" :
        {"message": "Неверный код верификации"}
      }
    }
  }

  // верификация прошла
  // обновляем статус сессии верификации в БД на 'used'
  // ....
  //генерим токен авторизации, записываем в базу и отдаем клиенту
  const { v4: uuidv4 } = require('uuid');
  const authToken = uuidv4(); // ⇨ '1b9d6bcd-bbfd-4b2d-9b5d-ab8dfbbd4bed'

  // добавляем юзер сессию с токеном авт-ции в таблицу сессий 
  // либо обновляем токен авт-ции если такой deviceid уже есть
  sql = 'SELECT * FROM user_sessions WHERE deviceid=?';
  params = [headers.deviceid];
  result = await require('./kernel-db.js')(sql, params);

  if (!result.length) {
    sql = 'INSERT INTO user_sessions (deviceid, userid, useragent, authtoken) VALUES (?,?,?,?)';
    params = [headers.deviceid, userRequest.tel, headers['user-agent'], authToken];
    result = await require('./kernel-db.js')(sql, params);
  } else {
    sql = 'UPDATE user_sessions SET authtoken=?,userid=? WHERE deviceid=?';
    params = [authToken, userRequest.tel, headers.deviceid];
    result = await require('./kernel-db.js')(sql, params);
  }

  // поставить текущий таймстепм в поле activated в таблице users 
  // причем тип аккаунта не важен - у обоих

  sql = 'UPDATE users SET activated=? WHERE tel=?';
  params = [new Date(), userRequest.tel];
  result = await require('./kernel-db.js')(sql, params);

  return { ...{ tel: userRequest.tel, authToken} };

};


exports.checkUserSession = async userRequest => {

  // проверка наличие сессии токена в бд
  console.log('inside checkUserSession с параметром:', userRequest);
  let sql, params, result;

  // есть ли такой токен в базе
  sql = 'SELECT * FROM `user_sessions` WHERE `authtoken` = ?';
  params = [userRequest];
  result = await require('./kernel-db.js')(sql, params);
  // токен не найден - выйти с ошибкой
  if (!result.length) return { 
    "error" : 
      {"message": "Неверный токен авторизации."}
    }

    
  // вытащим юзер айди из основной таблиц по его тел
  let sqlUser, paramsUser, resultUser;
  sqlUser = 'SELECT * FROM `users` WHERE `tel` = ?';
  paramsUser = [result[0].userid];
  resultUser = await require('./kernel-db.js')(sqlUser, paramsUser);

  // // вытащим юзер айди из user_my по его тел
  // let sqlUserLinked, paramsUserLinked, resultUserLinked;
  // sqlUserLinked = 'SELECT * FROM `users_my` WHERE `tel` = ?';
  // paramsUserLinked = [result[0].userid];
  // resultUserLinked = await require('./kernel-db.js')(sqlUserLinked, paramsUserLinked);

  // if (!resultUser.length && !resultUserLinked.length) return { 
  if (!resultUser.length) return { 
    "error" : 
      {"message": "Странная ошибка - не найден юзер по тел"}
    }
    
  result[0].id = resultUser[0].id;
  result[0].type = resultUser[0].type; // main | linked
  result[0].mainUserID = resultUser[0].mainUserID; // ID мейна
  result[0].focusGroup = resultUser[0].focusGroup; // признак члена фокус-группы
  // перестраховка если linkedUserPermissions в БД = NULL, то в пустую строку, чтобы ошибкок не было дальше
  result[0].linkedUserPermissions = resultUser[0].linkedUserPermissions 
    ? resultUser[0].linkedUserPermissions 
    : ''; 
  

  // if (resultUser.length) {
  //   // main
  //   result[0].id = resultUser[0].id;
  //   result[0].type = resultUser[0].type; // main
  // } else {
  //   // linked
  //   result[0].id = resultUserLinked[0].id;
  //   result[0].type = resultUserLinked[0].type; // main
  // }

  // дополним ответ айдишником юзера чтоюы записать его в глоб переменную
  // немного перетасуем названия полей
  result[0].sessionID = result[0].id;
  result[0].tel = result[0].userid;
  return result[0];
}

exports.getUserData = async (userRequest) => {
  // userRequest = global.user
  // проверка наличие сессии токена в бд
  console.log('inside getUserData с параметром:', userRequest);
  let sql, params, result;
  const userData = {};

  // выберем поля в завис-сти от типа аккаунта
  // временно вернул линкеду myCarsLimit, myUsersLimit тк ios падал без них, убери через версию
  if (userRequest.type === ENV.UserTypeMain) {
    sql = 'SELECT id, type, tel, display_name, chosenSurName, chosenName, chosenMiddleName, email, emailVerified, myCarsLimit, myUsersLimit, dpaSignedDate, created, activated FROM `users` WHERE `id` = ?';
  } else {
    sql = 'SELECT id, tel, type,  display_name, chosenSurName, chosenName, chosenMiddleName, email, emailVerified, dpaSignedDate, mainUserID, info, created, activated, linkedUserPermissions, myCarsLimit, myUsersLimit  FROM `users` WHERE `id` = ?';
  };
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  // юзер не найден - выйти с ошибкой
  if (!result.length) return { 
    "error" : 
      {"message": "Не найден пользователь с таким телефоном."}
    }

  // Добавляем осн параметры в модель которую потом отдадим клиенту
  userData.user = {};
  const hasAccessTo = [];
  userData.user.hasAccessTo = [];
  // userData.user.id = result[0].id;
  // userData.user.type = result[0].type;
  // userData.user.lastVisited = "";
  // userData.user.chosenName = result[0].chosenName;
  userData.user = result[0];
  userData.user.tel = (result[0].tel).toString();
  
  userData.user.chosenSurName = `${result[0].chosenSurName[0]}.`; // скрываем Фамилию в настройках
  userData.user.display_name = result[0]. display_name || `${result[0].chosenName} ${result[0].chosenMiddleName} ${result[0].chosenSurName[0]}.`; // заменяем ФИО на 1 строку

  if (userData.user.dpaSignedDate) userData.user.dpaSignedDate = formatISOTimeStamp(userData.user.dpaSignedDate);
  
  // в зависимости от типа текущего аккаунта и его разрешений, запрашиваем список ЛС
  // userData.user.accounts = []; // список ЛС сначала пустой массив

  // если текущий юзер - мейн
  if (userRequest.type === ENV.UserTypeMain) {
    params = [userRequest.id]; // во всех запросах блока исп-уем id текущего юзера
    // получаем список ЛС для main
    sql = 'SELECT id, bitrixOuterCode AS accountNumber, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
    result = await require('./kernel-db.js')(sql, params);
    userData.user.accounts = result; // добавляем как поле accounts к основному ответу
    
    // TODO на основании полученных типов ЛС вычисляем доступ к подразделам заявок на пропуска
    // пока разрешим доступ по умолчанию к обоим разделам пропусков и к остальным тоже
    console.log("userData.user.hasAccessTo = ", userData.user.hasAccessTo);
    hasAccessTo.push("accounts");
    hasAccessTo.push("meters");
    hasAccessTo.push("passAuto");
    hasAccessTo.push("passPerson");
    hasAccessTo.push("servicesEmail");
    hasAccessTo.push(ENV.SectionAccounts);
    // hasAccessTo.push(ENV.SectionMeters);
    // hasAccessTo.push(ENV.SectionPassAuto);
    // hasAccessTo.push(ENV.SectionPassPerson);
    // if (userRequest.focusGroup && !userRequest.useragent.includes("iOS")) {
    if (userRequest.focusGroup) {
      hasAccessTo.push('services'); // доб новые заявки
      // удаляем звонок в дисп
      // todo раскаментить когда ios заапрувят иначе тупо исчезнут все заявки
      // const index = hasAccessTo.indexOf('servicesEmail');
      // if (index > -1) { // only splice array when item is found
      //   hasAccessTo.splice(index, 1); // 2nd parameter means remove one item only
      // }
    };
    // hasAccessTo.push('contacts'); // TODO убрать в .env
    // разрешаем отправлять показания пока только мейнам из фокус группы
    // if (sendMeterValuesPermission && userRequest.focusGroup === 1) hasAccessTo.push(ENV.OptionSendMeterValues); 
    // апдейт 15.04.23 - разрешаем отправлять показания всем мейнам
    // if (sendMeterValuesPermission) hasAccessTo.push(ENV.OptionSendMeterValues); 
    hasAccessTo.push("news");
    hasAccessTo.push("qrPaymentButton");
    hasAccessTo.push("acquiring");

    // только для мейна: по id юзера получаем список его авто
    sql = 'SELECT * FROM `cars` WHERE `userID` = ?';
    result = await require('./kernel-db.js')(sql, params);
    // преобразуем формат всех таймстемпов
    for (const [index, row] of result.entries()) {
      result[index].ts = formatISOTimeStamp(result[0].ts);
      result[index].activated = result[index].activated ? true : false;
    }
    userData.user.myCars = result;

    // только для мейна: получаем лимит авто юзера по сумме лимитов всех его ЛС
    let sqlLimit, paramsLimit, resultLimit;
    sqlLimit = 'SELECT SUM(carLimit) AS lim FROM `accounts` WHERE userID = ?';
    paramsLimit = params; // id текущего юзера, определено в начале блока
    resultLimit = await require('./kernel-db.js')(sqlLimit, paramsLimit);
    userData.user.myCarsLimit = Number(resultLimit[0].lim); // строку в число
  
    // только для мейна: по id юзера получаем список его linked аккаунтов
    sql = 'SELECT * FROM `users` WHERE `mainUserID` = ?';
    result = await require('./kernel-db.js')(sql, params);
    // преобразуем поле linkedUserPermissions из строки в массив у всех найденных линкедов
    for (const [index, row] of result.entries()) {
      result[index].linkedUserPermissions = result[index].linkedUserPermissions ?
        row.linkedUserPermissions.split(',') : [];
    };
    userData.user.myUsers = result;

    // считаем общую сумму долга по всем квитанциям
    sql = 'SELECT SUM(dolg_out) as debt FROM `dolgi` WHERE account_number1 IN (SELECT bitrixOuterCode FROM accounts WHERE userID = ?) AND account_number2 IN (SELECT bitrixOuterCode2 FROM accounts WHERE userID = ?)';
    paramsDebt = [userRequest.id, userRequest.id];
    // sql = 'SELECT SUM(amount) as debt FROM `bills` WHERE accountUnitedID IN (SELECT bitrixOuterCode FROM accounts WHERE userID = ?)';
    result = await require('./kernel-db.js')(sql, paramsDebt);
    userData.user.debt = formatMysqlDecimalToCur(result[0].debt);

    // приводим к массиву поле linkedUserPermissions
    // удалить когда выкатим iOS который не ждет это поле в мейне, а пока падает
    userData.user.linkedUserPermissions = userData.user.linkedUserPermissions ?
      userData.user.linkedUserPermissions.split(',') : [];
  };

  // если юзер линкед то пробросим в поле hasAccessTo разрешения из поля linkedUserPermissions
  if (userRequest.type === ENV.UserTypeLinked) {
    // userRequest.linkedUserPermissions.split(",").map(el => userData.user.hasAccessTo.push(el));
    if (userRequest.linkedUserPermissions) userRequest.linkedUserPermissions.split(",").map(el => hasAccessTo.push(el));
    
    // приводим к массиву поле linkedUserPermissions
    userData.user.linkedUserPermissions = userData.user.linkedUserPermissions ?
      userData.user.linkedUserPermissions.split(',') : [];

    // TODO на основании полученных типов ЛС вычисляем доступ к подразделам заявок на пропуска
    // пока всем линкедам разрешим доступ по умолчанию к обоим разделам пропусков
    // hasAccessTo.push("passAuto");
    // hasAccessTo.push("passPerson");
    hasAccessTo.push(ENV.SectionPassAuto);
    hasAccessTo.push(ENV.SectionPassPerson);
    
    // для всех линкедов: если линкеду доступен раздел Счетчики И актвино окно подачи показаний, то добавим разрешение на отправку показаний
    if (userData.user.linkedUserPermissions.includes(ENV.SectionMeters) && sendMeterValuesPermission) hasAccessTo.push(ENV.OptionSendMeterValues);
    // для линкедов только из фокус-группы: если линкеду доступен раздел Счетчики И активно окно подачи показаний И у линкеда активен флаг фокус-группы, то добавим разрешение на отправку показаний
    // if (userData.user.linkedUserPermissions.includes(ENV.SectionMeters) && sendMeterValuesPermission && userRequest.focusGroup === 1) hasAccessTo.push(ENV.OptionSendMeterValues);
  };

  // если текущий юзер - линкед с доступом к ЛС мейна
  if (userRequest.type === ENV.UserTypeLinked && userRequest.linkedUserPermissions.includes(ENV.SectionAccounts)) {
    params = [userRequest.mainUserID]; // во всех запросах блока исп-уем id мейна
    // получаем список ЛС мэйна для линкеда, у которого есть доступ
    sql = 'SELECT id, bitrixOuterCode AS accountNumber, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
    result = await require('./kernel-db.js')(sql, params);
    userData.user.accounts = result; // добавляем как поле accounts к основному ответу

    // считаем общую сумму долга по всем квитанциям
    sql = 'SELECT SUM(amount) as debt FROM `bills` WHERE accountUnitedID IN (SELECT bitrixOuterCode FROM accounts WHERE userID = ?)';
    result = await require('./kernel-db.js')(sql, params);
    userData.user.debt = formatMysqlDecimalToCur(result[0].debt);
  }
  
  userData.user.hasAccessTo = hasAccessTo;
  userData.user.address = "ул. Покрышкина, 8";

  return userData;
}

exports.updateUserData = async (userID, userRequest, globalUser) => {
  // userID = global.user.id
  // userRequest = {chosenName/chosenMiddleName/chosenSurName/email}
  // globalUser = global.user
  console.log('inside updateUserData с параметром:', userRequest);
  let sql, params, result
  let userData = {};

  // формируем SQL-запрос
  // нужно включить на апдейт только те поля, которые есть в userRequest
  // и только из разрешенного списка

  // массив всех полей пришедших в запросе
  console.log("userRequest", userRequest);
  const reqFields = Object.keys(userRequest);

  // ГАРД на попытку обновления ФИО и emeail у мейна, но не на апдейт согласия с ПК
  // ориентируемся на отсутсвие параметра dpaSignedDate в запросе
  // таким образом понятно что запрос идет не с формы согласия с ПД
  if (globalUser.type === 'main' && !userRequest.dpaSignedDate) {
    return { 
      "error" : 
      {"message": `Для обновления ФИО или e-mail обратитесь в офис ЖК`}
    }
  };

  // массив разрешенных к апдейту полей
  let canBeUpdatedFields = [];
  if (globalUser.type === 'main') {
    canBeUpdatedFields = [
      {reqFieldName: "dpaSignedDate", tableFieldName: "dpaSignedDate"},
    ];
  } else {
    canBeUpdatedFields = [
      {reqFieldName: "display_name", tableFieldName: "display_name"},
      {reqFieldName: "chosenName", tableFieldName: "chosenName"},
      {reqFieldName: "chosenMiddleName", tableFieldName: "chosenMiddleName"},
      {reqFieldName: "chosenSurName", tableFieldName: "chosenSurName"},
      {reqFieldName: "email", tableFieldName: "email"},
      {reqFieldName: "info", tableFieldName: "info"},
      {reqFieldName: "dpaSignedDate", tableFieldName: "dpaSignedDate"},
    ];
  }
  
  // массив полей который пришел с клиоента 
  const toUpdateFields = canBeUpdatedFields.filter ( field => reqFields.includes(field.reqFieldName) );
  console.log("массив полей, пришедших в запросе на обновление", toUpdateFields);
  
  // склеиваем результирующую строку запроса
  sql = toUpdateFields.reduce ( (prev, current) => prev + current.tableFieldName + ' = ?,', '');
  sql = sql.slice(0, -1); // удяляем посл запятую https://stackoverflow.com/a/952945/6056120
  sql = `UPDATE users SET ${sql} WHERE id = ?`; // формируем окончательный вид строки запроса
  console.log("строка SQL к апдейту: ", sql);
  
  // cформируем массив подстановочных значений вместо ?
  params = toUpdateFields.map( field => userRequest[field.reqFieldName]);
  // .. и добавим к нему последний параметр userID для WHERE id = ?
  params.push(userID);
  console.log("params к апдейту: ", params);
  

  // sql = 'UPDATE `users` SET chosenName = ? WHERE `id` = ?';
  // params = [userRequest.chosenName, userID];
  result = await require('./kernel-db.js')(sql, params);
  // юзер не найден - выйти с ошибкой
  // if (!result.length) return { 
  //   "error" : 
  //     {"message": "Не найден пользователь с таким id."}
  //   };
  
  // запросим полную модель юзера и вернем клиенту
  // TODO список полей которые надо вернуть возможно стоит ограничить, вместо *
  sql = 'SELECT * FROM `users` WHERE `id` = ?';
  params = [userID];
  result = await require('./kernel-db.js')(sql, params);

  // userData = { 
  //   "type": "main",
  //   "tel": userRequest.tel,
  //   "chosenName": userRequest.chosenName,
  //   "lastVisited": ""
  // }

  // TODO тут видимо придется дозапрашивать из других таблиц данные 
  // о списке его авто и завис аккаунтов и джйнить их к основному ответу

  userData = result[0];

  // считаем общую сумму долга по всем квитанциям
  sql = 'SELECT SUM(amount) as debt FROM `bills` WHERE accountUnitedID IN (SELECT unitedID FROM accounts WHERE userID = ?)';
  params = [userID];
  result = await require('./kernel-db.js')(sql, params);
  userData.debt = formatMysqlDecimalToCur(result[0].debt);

  return userData;
}

exports.getPassList = async (userRequest, userTel) => {
  // получение списка пропусков по айди аккаунта
  console.log('inside getPassList с параметром:', userRequest, userTel);
  let sql, params, result;

  // если userRequest содержит userID то возвращаем только его заявки
  // если не содержит - то возвращаем все (охрана)

  // TODO удалить ненужные ветки SQL-запроса для АРМ

  if (userTel === ENV.TelKppPersonKit) {
    sql = 'SELECT * FROM `passes` WHERE `type` = "person" ORDER BY dateCreated DESC';
  } else if (userTel === ENV.TelKppAutoKit) {
    sql = 'SELECT * FROM `passes` WHERE `type` = "auto" ORDER BY dateCreated DESC';
  } else if (userTel === ENV.TelKppAutoPersonKit) {
    sql = 'SELECT * FROM `passes` WHERE `type` = "person" OR `type` = "auto" ORDER BY dateCreated DESC';
  } else {
    // sql = 'SELECT * FROM `passes` WHERE `userID` = ? ORDER BY dateCreated DESC';
    sql = `SELECT * FROM passes WHERE userID = ? AND (DATEDIFF(CURRENT_TIMESTAMP(), dateCreated) < 15 OR favorite = 1 OR status = 'open' OR status = 'delivered') ORDER BY dateCreated DESC`;
    params = [userRequest];
  }
  
  result = await require('./kernel-db.js')(sql, params);
  
  const passList = [];
  
  for (const row of result) {
    // форматируем все поля даты в респонсе перед отдачей клиенту
    row.dateCreated = formatISOTimeStamp(row.dateCreated);
    row.dateClosed = row.dateClosed ? formatISOTimeStamp(row.dateClosed) : null;
    row.dateDelivered = row.dateDelivered ? formatISOTimeStamp(row.dateDelivered) : null;
    row.specialNumberFormat = row.specialNumberFormat ? true : false;
    row.favorite = row.favorite ? true : false;

    // TODO протестировать работоспособность АРМ без след. блока и удалить его
    // TODO также удалить не нужные ветки SQL-запроса вначале
    // // добавляем поле issuer - только при запросах из прил АРМ озраны 
    // if (userTel === ENV.TelKppAutoKit || userTel === ENV.TelKppPersonKit || userTel === ENV.TelKppAutoPersonKit)
    // {
    //   let sqlInner, paramsInner, resultInner;
    //   sqlInner = 'SELECT * FROM `users` WHERE `id` = ?';
    //   paramsInner = [row.userID];
    //   resultInner = await require('./kernel-db.js')(sqlInner, paramsInner);
    //   // проверка на наличие юзера, тк он бывает уже удален, а его пропуск в бд есть
    //   // из-за этого эксепшен на undefined.id ниже 
    //   // нужна целостность связи между таблицами, тк каскадное удаление по FK
    //   // в табл users удален user с id - сразу удалить его заявки в табл passes с этим userID
    //   if (resultInner.length) { 
    //     // поиск по квартирам
    //     let sqlInnerIssuer, paramsInnerIssuer, resultInnerIssuer, flatList, fromAptConcat;
    //     sqlInnerIssuer = 'SELECT * FROM `accounts` WHERE `userID` = ?';
    //     paramsInnerIssuer = resultInner[0].type === 'main' ? [resultInner[0].id] : [resultInner[0].mainUserID];
    //     resultInnerIssuer = await require('./kernel-db.js')(sqlInnerIssuer, paramsInnerIssuer);
    //     fromAptConcat = ''; // сюда склеим квартиры или мм

    //     // for (const innerIssuerRow of resultInnerIssuer) {}

    //     flatList = resultInnerIssuer.filter( row => row.accountType === 'appartment'); // выделим ЛС типа квартир
    //     if (!flatList.length) flatList = resultInnerIssuer.filter( row => row.accountType === 'auto'); // если нет кв, то м/м
    //     fromAptConcat = flatList.reduce( (prev, current) => prev + current.apptNumberFull + "; ", ""); //  склеим № кв
    //     row.issuer = {
    //       "accountID": resultInner[0].id, // на самом деле id юзера, чтобы не менять поле на клиенте АРМ
    //       "accountType": resultInner[0].type,
    //       "address": "Покрышкина, 8", // пока захардокить
    //       "number": fromAptConcat, // пока захардокить
    //       // "number": "тел: +" + resultInner[0].tel, // пока захардокить
    //       // "tel": resultInner[0].tel,
    //       // "name": resultInner[0].chosenName + ' ' + resultInner[0].chosenMiddlwName + ' ' + resultInner[0].chosenSurName,
    //       // "accountType": resultInner[0].accountType,
    //       // "address": resultInner[0].addressConcat,
    //       // "number": resultInner[0].apptNumberFull
    //     };
    //   };
    // };
    
    console.log("row: ", row);
    passList.push(row);
  }

  return passList;
}

exports.createPass = async (userRequest, userID, globalUser) => {
  // создание заявки на пропуск в бд
  console.log('inside createPass с параметром:', userRequest);
  let sql, params, result, newPassId, newPass, resultPush, autoNumberGost;
  
  // проверка наличия поля specialNumberFormat
  const specialNumberFormat = userRequest.specialNumberFormat ? true : false;
  if (userRequest.type === 'auto' && !specialNumberFormat) {
    // проверка стандартного номера на ГОСТ только для заявок авто
    autoNumberGost = checkAutoNumber(userRequest.autoNumber);
    if (!autoNumberGost) {
      return { 
        "error" :
        {"message": "Номер авто не соответствует ГОСТ"}
      };
    };
  };

  // проверка поля autoNumberType на соотв-вие одному из типов (enum)
  const autoPassType = ['standard','taxi','special'];
  if (userRequest.type === 'auto' && userRequest.autoNumberType && !autoPassType.includes(userRequest.autoNumberType)) {
    return { 
      "error" :
      {"message": "Доступные значения autoNumberType: 'standard','taxi','special'"}
    };
  };

  // if (userRequest.type === 'auto' && globalUser.focusGroup !== 1) {
  //   // запрет заявко на АРМ авто для всех кроме фокус группы
  //   return { 
  //     "error" :
  //     {"message": "Сервис заказа пропуска на автотранспорт через приложение временно не доступен."}
  //   };
  // };

  // замена отсутсвтующих параметров на null чтобы не вылетала ошибка
  const courier = userRequest.courier !== undefined ? userRequest.courier : null;
  const personAmount = userRequest.personAmount !== undefined ? userRequest.personAmount : null;
  const autoBrand = userRequest.autoBrand !== undefined ? userRequest.autoBrand : null;
  const favorite = userRequest.favorite !== undefined ? userRequest.favorite : 0;
  const autoNumberType = userRequest.autoNumberType !== undefined ? userRequest.autoNumberType : null;

  // приводим autoNumber к универсальному виду
  let autoNumber;
  if (userRequest.type === 'auto') {
    // для заявок типа auto
    autoNumber = !specialNumberFormat
    ? autoNumberGost // пишем в БД номер по ГОСТ
    : userRequest.autoNumber.replace(/\s+/g, ''); // пишем в БД переданный нестандартный номер, убрав пробелы
  } else {
    // для заявок типа person чтобы не пропускать undefined в SQL
    autoNumber = null;
  };

  sql = 'INSERT INTO passes (userID, type, courier, personAmount, autoNumber, specialNumberFormat, autoBrand, status, favorite, autoNumberType, description_text) VALUES (?,?,?,?,?,?,?,?,?,?,?)';
  params = [userID, userRequest.type, courier, personAmount, autoNumber, specialNumberFormat, autoBrand, ENV.PassStatusOpen, favorite, autoNumberType, userRequest.description_text || ''];
  result = await require('./kernel-db.js')(sql, params);

  console.log('создана запись на пропуск:', result);
  newPassId = result.insertId;

  // отправим запрос на добавление в цвс для стандартного номеров только от членов фокус-группы
  if (!specialNumberFormat && globalUser.focusGroup === 1) {
    const groupId = 8 // Группа 6. Заменить нормальной логикой

    // добавление номера в cvs
    // подготовка body запроса
    const data = {
      "plate": autoNumberGost,
      "dateTime": new Date(),
      "expireDateTime":  new Date(new Date().getTime() + (24 * 60 * 60 * 1000)), // +24часа
      "groupId": groupId, // группу из бд динамически брать
      // "agree": 0,
      // "active": 0
    };

    // TODO убрать в асинхронный поток запрос к ресту CVS и обработчик ответа

    // const addCarToCVS = await require('./kernel-cvs.js')("reglp","post", data);
    const addCarToCVS = {}; // удали эту строку как восстановишь CVS

    // если все ок - в addCarToCVS.data дб полная модель добавленной записи в бд CVS
    if (addCarToCVS.data) {
      console.log("Ответ от CVS:", addCarToCVS.data);
      const idCvs = addCarToCVS.data.plates[0].id;
      let sql, params, result;
      sql = 'UPDATE `passes` SET idCvs=?, groupIdCvs=?, dateCreatedCvs=? WHERE id=?';
      params = [idCvs, groupId, new Date(), newPassId];
      result = await require('./kernel-db.js')(sql, params);
    }

    if (addCarToCVS.response) console.log("Ответ от CVS: status ", addCarToCVS.response.status);
  };

  // запросим только что созданный пропуск со всеми полями чтобы вернуть клиенту
  sql = 'SELECT * FROM `passes` WHERE `id`=?';
  params = [newPassId];
  result = await require('./kernel-db.js')(sql, params);
  result[0].dateCreated = formatISOTimeStamp(result[0].dateCreated);
  result[0].specialNumberFormat = result[0].specialNumberFormat ? true : false;
  result[0].favorite = result[0].favorite ? true : false;
  newPass = result[0];

  // отправим пуш-уведомления на планшеты АРМ охраны
  sql = 'SELECT * FROM `user_sessions` WHERE `userid`=? OR `userid`=? OR `userid`=?';
  params = [ENV.TelKppAutoKit,ENV.TelKppPersonKit,ENV.TelKppAutoPersonKit];
  result = await require('./kernel-db.js')(sql, params);
  
  // отправим пуши на все найденные токены ус-в
  console.log('Пуштокены планшетов: ', result)
  for (const row of result) {
    resultPush = await require('./kernel-push.js')(
      {
        "pushToken": row.pushtoken,
        "title": "newPass",
        "body": `${newPassId}`
      }
    );
  };
  
  
  return newPass;
}

exports.updatePassStatus = async userRequest => {
  // обновление статуса заявки пропуска
  console.log('inside updatePassStatus с параметром:', userRequest);
  let sql, params, result;

  // проверяем какой статус пришел 
  if (userRequest.status === ENV.PassStatusDelivered) {
    sql = 'UPDATE passes SET status=?, dateDelivered=? WHERE id=?';
  } else {
    sql = 'UPDATE passes SET status=?, dateClosed=? WHERE id=?';
  }
  params = [userRequest.status, new Date(), userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  
  // Заявка закрыта - уведомить пушами на все ус-ва юзера
  if ([ENV.PassStatusDelivered, ENV.PassStatusClosed].includes(userRequest.status) ) {
    // Как узнать пуштокены всех девайсов юзера
    // 1. Получим accountID ЛС к которому привязана заявка в запросе
    // sql = 'SELECT accountID FROM `passes` WHERE id=?';
    // params = [userRequest.id];
    // result = await require('./kernel-db.js')(sql, params);
    // 2. Получим userID юзера к которому привязан пропуск
    sql = 'SELECT userID FROM `passes` WHERE id=?';
    params = [userRequest.id];
    result = await require('./kernel-db.js')(sql, params);
    // 3. Получим tel юзера c найденным userID
    sql = 'SELECT tel FROM `users` WHERE id=?';
    params = [result[0].userID];
    result = await require('./kernel-db.js')(sql, params);
    // 4. Получим пуштокены юзера c найденным tel
    sql = 'SELECT pushtoken FROM `user_sessions` WHERE userid=?';
    params = [result[0].tel];
    result = await require('./kernel-db.js')(sql, params);
    
    // отправим пуши на все найденные токены ус-в
    console.log('Пуштокены юзера: ', result);
    let message;
    if (userRequest.status === ENV.PassStatusDelivered) message = 'принята';
    if (userRequest.status === ENV.PassStatusClosed) message = 'выполнена';
    for (const row of result) {
      resultPush = await require('./kernel-push.js')(
        {
          "pushToken": row.pushtoken,
          "title": "Смена статуса заявки на пропуск.",
          "body": `Ваша заявка ${message}`
        }
      );
    }
  }

  // Заявка отменена самим пользователем
  if (userRequest.status === ENV.PassStatusCanceled) {

    // обновим в БД бэка поля status и dateCanceled
    // TODO убрать смену статуса в начале метода, тк перенесли сюда
    sql = 'UPDATE passes SET status=?, dateCanceled =? WHERE id=?';
    params = [ENV.PassStatusCanceled, new Date(), userRequest.id];
    result = await require('./kernel-db.js')(sql, params);

    // получим idCvs отменяемого пропуска чтобы удалить из бд цвс
    sql = 'SELECT * FROM `passes` WHERE id=?';
    params = [userRequest.id];
    result = await require('./kernel-db.js')(sql, params);
    const idCvs = result[0].idCvs;

    // запрос на удаление в ЦВС отправляем только по тем пропускам, по котором в бэке есть idCvs
    // по сути это только автопропуска со стандартным номером - другие заявки в БД CVS не добавляются
    if (idCvs) {
      // TODO убрать в асинхронный поток запрос к ресту CVS и обработчик ответа
      const deletePassFromCVS = await require('./kernel-cvs.js')(`reglp/${idCvs}`, `delete`, ``);

      // если из ЦВС пришлок ОК, проапдетим поле dateCanceledCvs в бэке
      if (deletePassFromCVS.data) {
        console.log("Ответ от CVS:", deletePassFromCVS.data);
        sql = 'UPDATE passes SET dateCanceledCvs =? WHERE id=?';
        params = [new Date(), userRequest.id];
        result = await require('./kernel-db.js')(sql, params);
      }
    };

    // отправим пуш-уведомления на планшеты АРМ охраны на все найденные токены ус-в
    sql = 'SELECT * FROM `user_sessions` WHERE `userid`=? OR `userid`=? OR `userid`=?';
    params = [ENV.TelKppAutoKit,ENV.TelKppPersonKit,ENV.TelKppAutoPersonKit];
    result = await require('./kernel-db.js')(sql, params);
    
    console.log('Пуштокены планшетов: для уведомления об отмене заявки ', result)
    for (const row of result) {
      resultPush = await require('./kernel-push.js')(
        {
          "pushToken": row.pushtoken,
          "title": "cancelPass",
          "body": `${userRequest.id}`
        }
      );
    };
  };

  return result;
}

exports.updatePushToken = async userRequest => {
  // обновление статуса заявки пропуска
  console.log('inside updatePushToken с параметром:', userRequest);
  let sql, params, result, message;

  // проверяем есть ли уже diveceid
  sql = 'SELECT * FROM `user_sessions` WHERE `deviceid` = ?';
  params = [userRequest.deviceid];
  result = await require('./kernel-db.js')(sql, params);
  
  if (!result.length) {
    sql = 'INSERT INTO `user_sessions` (deviceid, pushtoken) values (?, ?)';
    params = [userRequest.deviceid, userRequest.pushtoken];
    message = 'Устройство добавлено в пул';
  } else {
    sql = 'UPDATE `user_sessions` SET pushtoken = ? WHERE deviceid = ?';
    params = [userRequest.pushtoken, userRequest.deviceid];
    message = 'Пуш-токен устройства обновлен';
  }
  result = await require('./kernel-db.js')(sql, params);
  return {message} ;
};

exports.writeMeterValues = async (userRequest, userID, globalUser) => {
  // в userRequest массив объектов показаний [{accountNumber:ХХХ, number:ХХХ, value:ХХХ}, { ... }, ...]
  console.log('inside writeMeterValues с параметром:', userRequest);
  let sqlMeters, paramsMeters, resultMeters;

  // запомним номер текущего юзера для фиксации в БД кем отправлен запрос
  let addedByUserID = userID;

  // если текущий юзер линкед, то в userID взять из mainUserID
  if (globalUser.type === 'linked') userID = globalUser.mainUserID;

  // ГАРД: юзер не из фокус-группы -- вернуть ошибку и выйти (апд: теперь можно всем)
  // if (globalUser.focusGroup !== 1) {
  //   return { 
  //     "error" :
  //     {"message": `Отправка показаний через приложение временно недоступна. Воспользуйтесь, пожалуйста, иными способами передачи показаний в ТСН.`}
  //   };
  // };

  // ГАРД: окно неактивно -- вернуть ошибку и выйти
  if (!sendMeterValuesPermission) {
    return { 
      "error" :
      {"message": `Период отправки показаний не открыт. Данные не переданы.`}
    };
  };

  // ГАРД: текущий юзер - линкед без прав на доступ к разделу счетчиков -- вернуть ошибку и выйти
  if (globalUser.type === 'linked' && !globalUser.linkedUserPermissions.includes(ENV.SectionMeters)) {
    return { 
      "error" :
      {"message": `Данные не обновлены -- у линкеда нет прав на доступ к разделу приборов учета воды в приложении.`}
    };
  };

  // получаем массив номеров всех счетчиков, пренадлежащих текущему юзеру по всем ЛС, для проверки прав
  sqlMeters = 'SELECT * FROM meters_1c WHERE account_number1 IN (SELECT bitrixOuterCode FROM accounts WHERE userID = ?) AND account_number2 IN (SELECT bitrixOuterCode2 FROM accounts WHERE userID = ?)';
  paramsMeters = [userID, userID];
  resultMeters = await require('./kernel-db.js')(sqlMeters, paramsMeters);
  resultMetersNumbers = resultMeters.map( el => el.number_manufacture ); // выведем все номера счетчиков в массив
  console.log("Массив номеров счетчиков юзера из БД, resultMetersNumbers:", resultMetersNumbers);
  
  // поиск и добавление в отдельный массив номеров счетчиков, присланных в запросе, но не пренадлежащих соб-ку
  const wrongMeters = [];
  for (const row of userRequest) {
    if (resultMetersNumbers.indexOf(row.number) === -1) {
      wrongMeters.push(row.number);
    };
  };

  // ГАРД если в запросе есть хотя бы один номер счетчика, не пренадлежащего юзеру -- выход из модуля без обновления данных
  if (wrongMeters.length) {
    return { 
      "error" :
      {"message": `Данные не обновлены -- указан неверный номер одного или нескольких счетчиков: [${wrongMeters.join(', ')}]. Список доступных для обновления номеров: [${resultMetersNumbers.join(', ')}].`}
    };
  };

  // ГАРД если в запросе есть показания, по длине не соответствующее разрядности счетчика integers+decimals -- выход из модуля без обновления данных
  // проверка на соот-вие длины строки поля value в запросе с суммой разрядов integers+decimals для данного счетчика
  const wrongValueLengthMeters = [];
  for (const row of userRequest) {
    const meter1c = resultMeters.find( el => el.number_manufacture === row.number); // нашли счетчик по номеру
    const sumOfDigits = meter1c.integers + meter1c.decimals; // получили его суммарную разрядность
    if (row.value.length !== sumOfDigits) wrongValueLengthMeters.push(`Длина значения [${row.value}], переданного по счетчику [${row.number}], не соответстует его разрядности [${sumOfDigits}].`);
  };
  // теперь смотрим есть ли ошибки
  if (wrongValueLengthMeters.length) {
    return { 
      "error" :
      {"message": `Данные не обновлены. ${wrongValueLengthMeters.join(' ')}`}
    };
  };

  // ГАРД если в запросе есть показания меньше, чем прошлые -- выход из модуля без обновления данных
  // проверка на показания, меньшие по сравнению с преддущими
  const decreasedValueMeters = [];
  for (const row of userRequest) {
    const meter1c = resultMeters.find( el => el.number_manufacture === row.number); // нашли счетчик по номеру
    const valueNumeric = parseInt(row.value) / Math.pow(10, (resultMeters.find( el => el.number_manufacture === row.number).decimals));
    if (valueNumeric < meter1c.last_readings_value) decreasedValueMeters.push(`Для счетчика [${row.number}]: новое показание [${valueNumeric}] не может быть меньше прошлого показания [${meter1c.last_readings_value}].`);
  };
  // теперь смотрим есть ли ошибки
  if (decreasedValueMeters.length) {
    return { 
      "error" :
      {"message": `Данные не обновлены. ${decreasedValueMeters.join(' ')} Для решения вопроса просьба обратиться в офис ТСН.`}
    };
  };

  // проверка номеров счетчиков пройдена -- записываем в БД показания
  let sql, params, result;
  for (const row of userRequest) {
    sql = 'INSERT INTO `meters_history` (`unitedID`, `number`, `value_str`, `value`, `source`, `ts`, `userID`, `addedByUserID`) VALUES (?,?,?,?,?,?,?,?)';
    params = [
      row.accountNumber,
      row.number,
      row.value,
      // переводим строковое показания value, переданное в запросе, в числовое, и делим его на 10 в степени decimals
      parseInt(row.value) / Math.pow(10, (resultMeters.find( el => el.number_manufacture === row.number).decimals)),
      'app', 
      new Date(),
      userID,
      addedByUserID
    ];
    result = await require('./kernel-db.js')(sql, params);
  };

  // если до этого места дошло и не вылетелол по ГАРДам, возвращаем ОК
  return "OK";

};


exports.getMeters = async userRequest => {
  // userRequest = global.user
  console.log('inside getMeters с параметром:', userRequest);

  // ГАРД если линкед не имеет прав на доступ к разделу счетчики -- выкидываем 422
  if (userRequest.type === ENV.UserTypeLinked && !userRequest.linkedUserPermissions.includes(ENV.SectionMeters)) {
    return { 
      "error" : 
        {"message": "У вас нет доступа к просмотру счетчиков этого аккаунта."}
      }
  };
  
  let sqlAccount, paramsAccount, resultAccount;

  if (userRequest.type === ENV.UserTypeMain) paramsAccount = [userRequest.id];
  if (userRequest.type === ENV.UserTypeLinked) paramsAccount = [userRequest.mainUserID];

  // по userID получаем список его ЛС
  sqlAccount = 'SELECT id, bitrixOuterCode AS accountNumber, bitrixOuterCode2 AS accountNumber2, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
  resultAccount = await require('./kernel-db.js')(sqlAccount, paramsAccount);
    
  // добавляем массив счетчиков к каждому аккаунту
  let sqlWM, paramsWM, resultWM;
  for (const [indexAccount, rowAccount] of resultAccount.entries()) {
    sqlWM = 'SELECT * FROM `meters_1c` WHERE `account_number1` = ? AND `account_number2` = ? AND conflicted_number IS NULL';
    paramsWM = [rowAccount.accountNumber, rowAccount.accountNumber2];
    resultWM = await require('./kernel-db.js')(sqlWM, paramsWM);

    resultAccount[indexAccount].meters = [];
    for (const [indexWM, rowWM] of resultWM.entries()) {
      resultWMOut = {};
      // собираем старую модель счетчика из новой модели таблицы
      resultWMOut.id = resultWM[indexWM].id;
      resultWMOut.bitrixID = 0;
      resultWMOut.accountID = resultAccount[indexAccount].id;
      resultWMOut.name = resultWM[indexWM].name || resultWM[indexWM].name_1c; // если name пустой то берем из name_1c
      resultWMOut.number = resultWM[indexWM].number_manufacture;
      resultWMOut.type = resultWM[indexWM].name_1c.includes('ХВС') ? "cold" : "hot"; // todo: учесть ещё teplo
      resultWMOut.serialCode = resultWM[indexWM].serial_code_1c;
      
      resultWMOut.nextCheckDate = formatRusDateToEuro(resultWM[indexWM].check_date); // приведем дату поверки к формату YYYY-MM-DD 00:00:00
      resultWMOut.serviceType = resultWM[indexWM].service_type;
      resultWMOut.integers = resultWM[indexWM].integers;
      resultWMOut.decimals = resultWM[indexWM].decimals;

      // поиск в таблице meters_history непереданные в 1с показания
      let last_readings_date, last_readings_value, last_readings_status;
      sqlWMHistory = 'SELECT * FROM `meters_history` WHERE `unitedID` = ? AND `number` = ? AND `uploaded1c` IS NULL ORDER BY ts DESC LIMIT 1';
      paramsWMHistory = [rowAccount.accountNumber, rowWM.number_manufacture];
      resultWMHistory = await require('./kernel-db.js')(sqlWMHistory, paramsWMHistory);
      // если есть непереданные показания то отдаем их с флагом 
      if (resultWMHistory.length && resultWMHistory[0].ts) {
        last_readings_date = formatISOTimeStamp(resultWMHistory[0].ts);
        last_readings_value = ((parseFloat(resultWMHistory[0].value)).toString()).replace('.',',');
        last_readings_status = 1;
      } else {
        // приведем дату после показаний к формату YYYY-MM-DD 00:00:00
        last_readings_date = formatRusDateToEuro(resultWM[indexWM].last_readings_date);
        // приведем последнее показание из meters_1c к формату float
        last_readings_value = ((parseFloat(resultWM[indexWM].last_readings_value)).toString()).replace('.',',');
        last_readings_status = 0;
      };
      // добавляем последние показания в модель счетчика
      resultWMOut.lastValue = {
        "date": last_readings_date ,
        "value": last_readings_value ,
        "status": last_readings_status,
        "value1c": parseFloat(resultWM[indexWM].last_readings_value),
        "date1c": formatRusDateToEuro(resultWM[indexWM].last_readings_date),
        "newValueLimit": parseFloat(resultWM[indexWM].new_value_limit)
      };

      // добавляем собарнную модель счетчика к текущему ЛС
      resultAccount[indexAccount].meters.push(resultWMOut);
      // добавляем массив всех счетиков
      resultAccount[indexAccount].metersAll = await require("./meters_test_new.json");
      resultAccount[indexAccount].test = "test";
    };

  }

  // оставить в ответе только ЛС со счетчиками
  // resultAccount = resultAccount.filter( rowAccount => rowAccount.meters.length > 0);
  // resultAccount = resultAccount.filter( rowAccount => rowAccount.metersAll.length > 0);

  return resultAccount;
};


exports.getMetersOld = async userRequest => {
  // userRequest = global.user
  console.log('inside getMetersOld с параметром:', userRequest);
  
  if (userRequest.type === ENV.UserTypeLinked && !userRequest.linkedUserPermissions.includes(ENV.SectionMeters)) {
    return { 
      "error" : 
        {"message": "У вас нет доступа к просмотру счетчиков этого аккаунта."}
      }
  };
  
  let sql, params, result;

  if (userRequest.type === ENV.UserTypeMain) params = [userRequest.id];
  if (userRequest.type === ENV.UserTypeLinked) params = [userRequest.mainUserID];

  // по userID получаем список его ЛС
  sql = 'SELECT id, bitrixOuterCode AS accountNumber, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
  result = await require('./kernel-db.js')(sql, params);
    
  // добавляем массив счетчиков к каждому аккаунту
  let sqlWM, paramsWM, resultWM;
  // https://www.30secondsofcode.org/articles/s/javascript-index-for-of-loop
  // чтобы вытащить индекс текущей итерации исп-ем хак с .entries()
  for (const [index, row] of result.entries()) {
    sqlWM = 'SELECT * FROM `meters` WHERE `accountID` = ?';
    paramsWM = [row.id];
    resultWM = await require('./kernel-db.js')(sqlWM, paramsWM);
    // добавим к счетчику объект с последними значениями показаний и формат даты
    for (const [indexWM, rowWM] of resultWM.entries()) {
      resultWM[indexWM].nextCheckDate = 
        resultWM[indexWM].nextCheckDate 
          ? formatISOTimeStamp(resultWM[indexWM].nextCheckDate) 
          : null;
        // найдем последние показания счетчика
        // sqlWMHistory = 'SELECT * FROM `meters_history` WHERE `number` = ? AND `source` = ? ORDER BY ts DESC LIMIT 1';
        // paramsWMHistory = [rowWM.number, 'bitrix'];
        sqlWMHistory = 'SELECT * FROM `meters_history` WHERE `number` = ? ORDER BY ts DESC LIMIT 1';
        paramsWMHistory = [rowWM.number];
        resultWMHistory = await require('./kernel-db.js')(sqlWMHistory, paramsWMHistory);
        if (resultWMHistory.length && resultWMHistory[0].ts) {
          resultWM[indexWM].lastValue = {
            // убираем десятичные нули справа (parseFloat), преобр в строку, и заменяем . на ,
            "value": ((parseFloat(resultWMHistory[0].value)).toString()).replace('.',','),
            "date": formatISOTimeStamp(resultWMHistory[0].ts)
          };
        }
    };
    result[index].meters = resultWM;
  }

  // оставить в ответе только ЛС со счетчиками
  result = result.filter( row => row.meters.length > 0);

  return result;
};

exports.addMyCar = async (userRequest, userID, globalUser) => {
  // добавление автомобиля
  console.log('inside addMyCar с параметром:', userRequest);
  let sql, params, result, message;

  if (!checkAutoNumber(userRequest.number)) {
    return { error: { message: "Номер авто не соответствует ГОСТ"}};
  };

  // замена отсутсвующего параметра на null чтобы избежать ошибки при INSERT
  const model = userRequest.model !== undefined ? userRequest.model : null;

  sql = 'INSERT INTO `cars` (userID, model, number) values (?, ?, ?)';
  params = [userID, model, checkAutoNumber(userRequest.number)];
  result = await require('./kernel-db.js')(sql, params);
  
  if (result.affectedRows !== 1) {
    return { error: { message: "Машина не добавилась в базу"}};
  };

  
  const newCarId = result.insertId; // id присвоенный MySQL-ем новой записи
  
  // если юзер из фокус-группы добавить номер в CVS
  if (globalUser.focusGroup === 1) {
  
    const groupId = 3 // заменить запросом к ЛС или юзеру

    // добавление номера в cvs
    // подготовка body запроса
    const data = {
      "plate": checkAutoNumber(userRequest.number),
      "dateTime": new Date(),
      "expireDateTime": "2023-11-17 15:00:00",
      "groupId": groupId, // группу из бд динамически брать
      // "agree": 0,
      // "active": 0
    };

    // TODO убрать в асинхронный поток запрос к ресту CVS и обработчик ответа

    // const addCarToCVS = await require('./kernel-cvs.js')("reglp","post", data);
    const addCarToCVS = {}; // убрать после починки CVS

    // если все ок - в addCarToCVS.data дб полная модель добавленной записи в бд CVS

    if (addCarToCVS.data) {
      console.log("Ответ от CVS:", addCarToCVS.data);
      const idCvs = addCarToCVS.data.plates[0].id;
      let sql, params, result;
      sql = 'UPDATE `cars` SET idCvs=?, groupIdCvs=?, activated=?, dateCreatedCvs=? WHERE id=?';
      params = [idCvs, groupId, true, new Date(), newCarId];
      result = await require('./kernel-db.js')(sql, params);
    }

    if (addCarToCVS.response) console.log("Ответ от CVS: status ", addCarToCVS.response.status);

  }

  // вернем Юре полную модельку добавленной машинки
  sql = 'SELECT * FROM `cars` WHERE `id` = ?';
  params = [newCarId];
  result = await require('./kernel-db.js')(sql, params);
  result[0].ts = formatISOTimeStamp(result[0].ts);
  result[0].activated = result[0].activated ? true : false;

  return { myCar: result[0]} ;
}


exports.getMyCars = async (userRequest) => {
  // userRequest = userID
  // получить список автомобилей юзера
  console.log('inside getMyCars:', userRequest);
  let sql, params, result;

  sql = 'SELECT * FROM `cars` WHERE `userID` = ?';
  params = [userRequest];
  result = await require('./kernel-db.js')(sql, params);

  // преобразуем формат всех таймстемпов
  for (const [index, row] of result.entries()) {
    result[index].ts = formatISOTimeStamp(result[0].ts);
    result[index].activated = result[index].activated ? true : false;
  }
  
  return { myCars: result} ;
}

exports.updateMyCar = async (userRequest, userID) => {
  // userRequest = { "id, "model", "number" }
  // апдейт инфы автомобиля
  console.log('inside updateMyCar с параметром:', userRequest);
  let sql, params, result;

  if (!checkAutoNumber(userRequest.number)) {
    return { error: { message: "Номер авто не соответствует ГОСТ"}};
  };

  // замена отсутсвующего параметра на null чтобы избежать ошибки при UPDATE
  const model = userRequest.model !== undefined ? userRequest.model : null;

  sql = 'UPDATE `cars` SET model=?, number=? WHERE id=?';
  params = [model, checkAutoNumber(userRequest.number), userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  
  if (result.affectedRows !== 1) {
    return { error: { message: `Машина c id = ${userRequest.id} не найдена в базе`}};
  }

  // вернем Юре полную модельку добавленной машинки
  sql = 'SELECT * FROM `cars` WHERE `id` = ?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  result[0].ts = formatISOTimeStamp(result[0].ts);
  result[index].activated = result[index].activated ? true : false;
  
  return { myCar: result[0]} ;
}

exports.deleteMyCar = async (userRequest, userID) => {
  // userRequest = "id"
  // удаление автомобиля
  console.log('inside deleteMyCar с параметром:', userRequest);
  let sql, params, result;

  // узнаем isCvs удаляемой машины
  sql = 'SELECT * FROM `cars` WHERE `id` = ?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  const idCvs = result[0].idCvs;

  // TODO убрать в асинхронный поток запрос к ресту CVS и обработчик ответа

  const deleteCarFromCVS = await require('./kernel-cvs.js')(`reglp/${idCvs}`, `delete`, ``);

  // если все ок - в deleteCarFromCVS.data

  if (deleteCarFromCVS.data) {
    console.log("Ответ от CVS:", deleteCarFromCVS.data);
    sql = 'DELETE FROM `cars` WHERE id=?';
    params = [userRequest.id];
    result = await require('./kernel-db.js')(sql, params);
  }

  return { affectedRows: result.affectedRows} ;
}

exports.getMyAccountsOld = async (userRequest) => {
  // userRequest = global.user
  // получить список аккаунтов юзера
  console.log('inside getMyAccounts:', userRequest);
  
  if (userRequest.type !== ENV.UserTypeMain && !userRequest.linkedUserPermissions.includes(ENV.SectionAccounts)) {
      return { 
      "error" : 
        {"message": "У вас нет доступа к просмотру раздела 'Лицевые счета' данного аккаунта."}
      }
  };
  
  let sql, params, result;

  if (userRequest.type === ENV.UserTypeMain) params = [userRequest.id];
  if (userRequest.type === ENV.UserTypeLinked && userRequest.linkedUserPermissions.includes(ENV.SectionAccounts)) params = [userRequest.mainUserID];

  sql = 'SELECT id, bitrixOuterCode AS accountNumber, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
  result = await require('./kernel-db.js')(sql, params);

  // добавляем квитанции к ЛС
  let sqlBills, paramsBills, resultBills;
  for (const [index, row] of result.entries()) {

    sqlBills = 'SELECT * FROM `bills` WHERE `accountUnitedID` = ?';
    paramsBills = [row.accountNumber];
    resultBills = await require('./kernel-db.js')(sqlBills, paramsBills);
    for (const [indexBills, rowBills] of resultBills.entries()) {
      resultBills[indexBills].period = formatDateToBillPeriod(resultBills[indexBills].period);
      resultBills[indexBills].amount = formatMysqlDecimalToCur(resultBills[indexBills].amount);
    };
    result[index].bills = resultBills;
  }
  
  return { accounts: result} ;
}

exports.getMyAccounts = async (userRequest) => {
  // userRequest = global.user
  // получить список аккаунтов юзера
  console.log('inside getMyAccounts:', userRequest);
  
  if (userRequest.type !== ENV.UserTypeMain && !userRequest.linkedUserPermissions.includes(ENV.SectionAccounts)) {
      return { 
      "error" : 
        {"message": "У вас нет доступа к просмотру раздела 'Лицевые счета' данного аккаунта."}
      }
  };
  
  let sql, params, result;

  if (userRequest.type === ENV.UserTypeMain) params = [userRequest.id];
  if (userRequest.type === ENV.UserTypeLinked && userRequest.linkedUserPermissions.includes(ENV.SectionAccounts)) params = [userRequest.mainUserID];

  // const BillModel = {
  //   "id": 432,
  //   "bitrixID": 0,
  //   "accountID": 15,
  //   "accountUnitedID": "**********",
  //   "accountBitrixID": 0,
  //   "title": "Капитальный ремонт, октябрь 2022, сумма: 1701.25",
  //   "type": "Капитальный ремонт",
  //   "period": "Октябрь 2022",
  //   "amount": "1 701,25 ₽",
  //   "url": "http://akademylux.ru/app/bills-coming-soon.htm"
  // };

  sql = 'SELECT id, bitrixOuterCode AS accountNumber, bitrixOuterCode2 AS accountNumber2, unitedID, accountType, addressConcat AS address, apptNumberFull FROM `accounts` WHERE `userID` = ?';
  result = await require('./kernel-db.js')(sql, params);

  // добавляем квитанции к ЛС
  let sqlBills, paramsBills, resultBills;
  let sqlBillDetails, paramsBillDetails, resultBillDetails;
  let sqlFileName, paramsFileName, resultFileName, file_name;
  let debt_account = 0, debt_total = 0; // для подсчета долгов - по каждому ЛС и общего

  for (const [index, row] of result.entries()) {

    // sqlBills = 'SELECT * FROM `dolgi` WHERE `account_number1` = ? AND `account_number2` = ?';
    sqlBills = 'SELECT id,account_number1,account_number2,pomeschenie,fio,amount_registered,amount_living,period_month,name_usl,dolg_in,nrm,izm,pay,SUM(dolg_out) as dolg_out,peni_flag FROM `dolgi` WHERE `account_number1` = ? AND `account_number2` = ? GROUP BY period_month,name_usl';
    paramsBills = [row.accountNumber, row.accountNumber2];
    resultBills = await require('./kernel-db.js')(sqlBills, paramsBills);
    const resultBillsOut = []; // собираем ответ, сохраняя названия полей из старого контракта для совм-сти

    // получим имя файла для квитанции с qr-кодом
    // sqlFileName = 'SELECT * FROM dolgi_qr WHERE account_number1 = ?';
    sqlFileName = 'SELECT * FROM dolgi_epd WHERE account_number1 = ?';
    paramsFileName = [row.accountNumber];
    resultFileName = await require('./kernel-db.js')(sqlFileName, paramsFileName);
    console.log(resultFileName);

    for (const [indexBills, rowBills] of resultBills.entries()) {
      resultBillsOut[indexBills] = {};
      resultBillsOut[indexBills].id = resultBills[indexBills].id;
      resultBillsOut[indexBills].period = formatDateToBillPeriodNew(resultBills[indexBills].period_month);
      resultBillsOut[indexBills].amount = formatMysqlDecimalToCur(resultBills[indexBills].dolg_out);
      resultBillsOut[indexBills].type = resultBills[indexBills].name_usl;
      // resultBillsOut[indexBills].url = resultFileName.length ? `http://127.0.0.1:51569/api/v2/assets/epd/${resultFileName[0].name}` : "";
      resultBillsOut[indexBills].url = resultFileName.length ? `https://app.uk-kit.ru:51579/api/v2/assets/epd/${resultFileName[0].name}` : "";
      resultBillsOut[indexBills].bitrixID = 0;
      resultBillsOut[indexBills].accountID = row.id;
      resultBillsOut[indexBills].accountUnitedID = row.accountNumber;
      resultBillsOut[indexBills].accountBitrixID = 0;
      resultBillsOut[indexBills].title = `${resultBills[indexBills].name_usl}, ${resultBillsOut[indexBills].period}, сумма: ${resultBillsOut[indexBills].amount}`;

      // получим детали квитанции начисления по услугам
      // sqlBillDetails = 'SELECT service AS service_name, end_period_sum AS service_dolg FROM dolgi_details WHERE account_number = ?';
      sqlBillDetails = 'SELECT service AS service_name, start_period_sum AS service_dolg FROM dolgi_details WHERE account_number = ?';
      paramsBillDetails = [row.accountNumber];
      resultBillDetails = await require('./kernel-db.js')(sqlBillDetails, paramsBillDetails);
      for (const [indexBillDetail, rowBillDetail] of resultBillDetails.entries()) {
        resultBillDetails[indexBillDetail].service_dolg = formatMysqlDecimalToCur(resultBillDetails[indexBillDetail].service_dolg);
      };
      resultBillsOut[indexBills].bill_details = resultBillDetails;
  
      // аккумулируем долг по всем квитанциям в текущем ЛС
      debt_account += Number(resultBills[indexBills].dolg_out);
    };

    result[index].bills = resultBillsOut;
    result[index].debt_account_numeric = debt_account*100; // сумма в копейках
    result[index].debt_account = formatMysqlDecimalToCur(debt_account);
    // result[index].debt_account_prompt = `на 14.11.24`;
    result[index].debt_account_prompt = `${debt_account<0?"Переплата":"Задолженность"} на ${ENV.dolgi_date_kit}`;
    // аккумулируем общий долг по всем ЛС
    debt_total += debt_account;
    debt_account = 0;
  }
  
  const data = {};
  data.accounts = result;
  data.debt_total = formatMysqlDecimalToCur(debt_total);

  return data ;
}


// TODO MY users
// TODO MY users
// TODO MY users
// TODO MY users
// TODO MY users
// TODO MY users
// TODO MY users



exports.addMyUser = async (userRequest, userID, globalUser) => {
  // userRequest = {tel, chosenName, chosenMiddleName, chosenSurName, info}
  // добавление пользователя

  console.log('inside addMyUser с параметром:', userRequest);

  // hotfix для ios - если пользователя создает не мейн - выкинуть ошибку
  if (globalUser.type !== ENV.UserTypeMain) return { 
    "error" : 
      {"message": `Добавление пользователей доступно только основным аккаунтам.`}
    };

  let sql, params, result;

  // ищем номер тел среди мастер аккаунтов
  sql = 'SELECT * FROM `users` WHERE `tel` = ?';
  params = [userRequest.tel];
  result = await require('./kernel-db.js')(sql, params);
  if (result.length) return { 
    "error" : 
      {"message": `Номер +${userRequest.tel} уже зарегистрирован в системе, укажите другой номер.`}
    };

  // замена отсутсвующего параметра на null чтобы избежать ошибки при INSERT
  const info = userRequest.info !== undefined ? userRequest.info : null;
  const chosenName = userRequest.chosenName !== undefined ? userRequest.chosenName : null;
  const chosenMiddleName = userRequest.chosenMiddleName !== undefined ? userRequest.chosenMiddleName : null;
  const chosenSurName = userRequest.chosenSurName !== undefined ? userRequest.chosenSurName : null;

  sql = 'INSERT INTO `users` (mainUserID, tel, display_name, chosenName, chosenMiddleName, chosenSurName, info, type, linkedUserPermissions) values (?, ?, ?, ?, ?, ?, ?, ?, ?)';
  params = [
    userID,
    userRequest.tel,
    userRequest.display_name || '',
    chosenName,
    chosenMiddleName,
    chosenSurName,
    info,
    ENV.UserTypeLinked,
    userRequest.linkedUserPermissions ? userRequest.linkedUserPermissions : ""
  ];
  result = await require('./kernel-db.js')(sql, params);
  
  if (result.affectedRows !== 1) {
    return { error: { message: "Пользователь не добавился в базу"}};
  }

  // вернем полную модельку добавленного юзера
  sql = 'SELECT * FROM `users` WHERE `id` = ?';
  params = [result.insertId];
  result = await require('./kernel-db.js')(sql, params);
  result[0].created = formatISOTimeStamp(result[0].created);
  result[0].linkedUserPermissions = result[0].linkedUserPermissions ?
    result[0].linkedUserPermissions.split(',') : [];

  return { myUser: result[0]} ;
}


// получить список моих юзеров
exports.getMyUsers = async (userRequest) => {
  // userRequest = userID
  console.log('inside getMyUsers:', userRequest);
  let sql, params, result;

  sql = 'SELECT * FROM `users` WHERE `mainUserID` = ?';
  params = [userRequest];
  result = await require('./kernel-db.js')(sql, params);

  // преобразу формат всех таймстемпов
  for (const [i, row] of result.entries()) {
    result[i].created = result[i].created ? formatISOTimeStamp(result[i].created) : result[i].created;
    result[i].activated = result[i].activated ? formatISOTimeStamp(result[i].activated) : result[i].activated;
    result[i].dpaSignedDate = result[i].dpaSignedDate ? formatISOTimeStamp(result[i].dpaSignedDate) : result[i].dpaSignedDate;
    // преобразуем поле linkedUserPermissions из строки в массив у всех найденных линкедов
    result[i].linkedUserPermissions = result[i].linkedUserPermissions ?
      row.linkedUserPermissions.split(',') : [];
  };
  
  return { myUsers: result} ;
}

exports.updateMyUser = async (userRequest, userID) => {
  // userID = global.user.id
  // userRequest = {id/chosenName/chosenMiddleName/chosenSurName/info}
  console.log('inside updateMyUser с параметром:', userRequest);
  let sql, params, result


  sql = 'SELECT * FROM `users` WHERE `id` = ?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);

  if (!result.length) return { 
    "error" : 
      {"message": `Пользователь с id=${userRequest.id} на найден.`}
    };


  // формируем SQL-запрос
  // нужно включить на апдейт только те поля, которые есть в userRequest
  // и только из разрешенного списка

  // массив всех полей пришедших в запросе
  console.log("userRequest", userRequest);
  const reqFields = Object.keys(userRequest);

  // массив разрешенных к апдейту полей
  const canBeUpdatedFields = [
    {reqFieldName: "display_name", tableFieldName: "display_name"},
    {reqFieldName: "chosenName", tableFieldName: "chosenName"},
    {reqFieldName: "chosenMiddleName", tableFieldName: "chosenMiddleName"},
    {reqFieldName: "chosenSurName", tableFieldName: "chosenSurName"},
    {reqFieldName: "info", tableFieldName: "info"},
    {reqFieldName: "dpaSignedDate", tableFieldName: "dpaSignedDate"},
    {reqFieldName: "linkedUserPermissions", tableFieldName: "linkedUserPermissions"},
  ];

  // массив полей который пришел с клиоента 
  const toUpdateFields = canBeUpdatedFields.filter ( field => reqFields.includes(field.reqFieldName) );
  console.log("массив полей, пришедших в запросе на обновление", toUpdateFields);
  
  // склеиваем результирующую строку запроса
  sql = toUpdateFields.reduce ( (prev, current) => prev + current.tableFieldName + ' = ?,', '');
  sql = sql.slice(0, -1); // удяляем посл запятую https://stackoverflow.com/a/952945/6056120
  sql = `UPDATE users SET ${sql} WHERE id = ?`; // формируем окончательный вид строки запроса
  console.log("строка SQL к апдейту: ", sql);
  
  // cформируем массив подстановочных значений вместо ?
  params = toUpdateFields.map( field => userRequest[field.reqFieldName]);
  // .. и добавим к нему последний параметр userID для WHERE id = ?
  params.push(userRequest.id);
  console.log("params к апдейту: ", params);
  

  // sql = 'UPDATE `users` SET chosenName = ? WHERE `id` = ?';
  // params = [userRequest.chosenName, userID];
  result = await require('./kernel-db.js')(sql, params);
  // юзер не найден - выйти с ошибкой
  // if (!result.length) return { 
  //   "error" : 
  //     {"message": "Не найден пользователь с таким id."}
  //   };
  
  // запросим полную модель юзера и вернем клиенту
  // TODO список полей которые надо вернуть возможно стоит ограничить, вместо *
  sql = 'SELECT * FROM `users` WHERE `id` = ?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);

  // userData = { 
  //   "type": "main",
  //   "tel": userRequest.tel,
  //   "chosenName": userRequest.chosenName,
  //   "lastVisited": ""
  // }

  // TODO тут видимо придется дозапрашивать из других таблиц данные 
  // о списке его авто и завис аккаунтов и джйнить их к основному ответу

  // userData = result[0];

  result[0].created = result[0].created ? formatISOTimeStamp(result[0].created) : result[0].created;
  result[0].activated = result[0].activated ? formatISOTimeStamp(result[0].activated) : result[0].activated;
  result[0].dpaSignedDate = result[0].dpaSignedDate ? formatISOTimeStamp(result[0].dpaSignedDate) : result[0].dpaSignedDate;
  result[0].linkedUserPermissions = result[0].linkedUserPermissions ? 
    result[0].linkedUserPermissions.split(',') : [];

  return {
    myUser: result[0]
  };

}

exports.deleteMyUser = async (userRequest, userID) => {
  // userRequest = "id"
  // удаление подчиненного юзера
  console.log('inside deleteMyUser с параметром:', userRequest);
  let sql, params, result;

  sql = 'DELETE FROM `users` WHERE id=? AND type=?'; // type=ENV.UserTypeLinked страховка, чтобы случайно не удалить main
  params = [userRequest.id, ENV.UserTypeLinked];
  result = await require('./kernel-db.js')(sql, params);
  
  // if (result.affectedRows !== 1) {
  //   return { error: { message: "Юзер не удален в базе"}};
  // }

  return { affectedRows: result.affectedRows} ;
}

exports.logout = async (authtoken, id) => {
  // удаление юзер сессии с указанным токенм
  console.log('inside logout с параметром:', authtoken);
  let sql, params, result;

  // удаление записи из таблицы сессий с данным токеном авторизации
  sql = 'DELETE FROM `user_sessions` WHERE authtoken = ?';
  params = [authtoken];
  result = await require('./kernel-db.js')(sql, params);
  
  if (result.affectedRows !== 1) {
    return { error: { message: "Сессия не удалена"}};
  }
  
  // обнулим нкоторые поля юзера в таблице users
  // sql = `UPDATE users SET dpaSignedDate = NULL WHERE id = ?`; // обнуляем статус согласия с ОПД
  // params = [id];
  // result = await require('./kernel-db.js')(sql, params);
  

  
  return { affectedRows: result.affectedRows} ;
}

exports.errorSend = async (user, headers, data) => {
  // удаление юзер сессии с указанным токенм
  console.log('inside errorSend с параметром:', data);
  let sql, params, result;

  // удаление записи из таблицы сессий с данным токеном авторизации
  sql = 'INSERT INTO `errors` (platform, osVersion, appVersion, method, stackTrace, globalUser, headers) values (?, ?, ?, ?, ?, ?, ?)';
  params = [data.platform, data.osVersion, data.appVersion, data.method, data.stackTrace, user, headers];
  result = await require('./kernel-db.js')(sql, params);
  
  if (result.affectedRows !== 1) {
    return { error: { message: "Ошибка не добавлена в БД"}};
  }
  
  return { } ;
}

exports.getPassListARM = async (userRequest, userTel) => {
  // получение списка пропусков по айди аккаунта
  console.log('inside getPassListARM с параметром:', userRequest, userTel);
  // console.log('проверка гит-флоу:', userRequest, userTel);
  let sql, params, result;

  // если запрос поступил не с ус-ва АРМ
  if (userTel !== ENV.TelKppAutoKit && userTel !== ENV.TelKppPersonKit && userTel !== ENV.TelKppAutoPersonKit) {
    return { error: { message: "Данный запрос возможен только с ус-ва АРМ охраны"}};
  };

  // тело запроса с полями 
  // "data": {
  //   "status": "open",
  //   "dateFrom": "2022-07-01 00:12:12",
  //   "dateTo": "2022-07-01 00:12:12",
  //   "queryStr": "51", - пока нет
  //   "limit": 50
  // }

  // let sqlStrWhere = `WHERE 1`;
  let sqlStrWhere = `WHERE `;
  
  if (userRequest.status === 'open') {
    // если "status": "open" то все заявки c "open" и "delivered"
    sqlStrWhere += `(status = 'open' OR status = 'delivered')`;
    // если есть dateTo
    sqlStrWhere += userRequest.dateTo ? ` AND dateCreated < '${userRequest.dateTo}'` : ``;
    // если есть dateFrom
    sqlStrWhere += userRequest.dateFrom ? ` AND dateCreated > '${userRequest.dateFrom}'` : ``;
  } else {
    // если "status": "closed" то все заявки c "closed" за последние сутки
    sqlStrWhere += `status = 'closed' AND dateCreated > SUBTIME(CURRENT_TIMESTAMP,'24:00:00')`;
  };

  // если в запросе есть айдишник, то возвращаем одну запись с ним
  if (userRequest.id) sqlStrWhere = `WHERE id = ${userRequest.id}`;

  // если запрос из КПП люди то заявки типа person
  if (userTel === ENV.TelKppPersonKit) sqlStrWhere += ` AND type = 'person'`;
  // если запрос из КПП авто то заявки типа auto (или любых курьеров - временно)
  if (userTel === ENV.TelKppAutoKit) sqlStrWhere += ` AND (type = 'auto' OR courier = 1)`;
  // в остальных случаях не ограничиваем выборку по типу type

  // если в запросе не указано поле limit то фолбэк на константу
  // UPD решили по умолчанию на ограничивать
  sqlStrLimit = userRequest.limit ? `LIMIT ${userRequest.limit}` : ``;

  // склеиваем результирующий запрос
  sql = `SELECT * FROM passes ${sqlStrWhere} ORDER BY dateCreated DESC ${sqlStrLimit}`;

  params = [];
  result = await require('./kernel-db.js')(sql, params, true);
  
  const passList = [];
  
  for (const row of result) {
    // форматируем все поля даты в респонсе перед отдачей клиенту
    row.dateCreated = formatISOTimeStamp(row.dateCreated);
    row.dateClosed = row.dateClosed ? formatISOTimeStamp(row.dateClosed) : null;
    row.dateDelivered = row.dateDelivered ? formatISOTimeStamp(row.dateDelivered) : null;
    row.specialNumberFormat = row.specialNumberFormat ? true : false;

    // в поле issuer подтягиваем инфу об отправителе заявки
    let sqlInner, paramsInner, resultInner;
    sqlInner = 'SELECT * FROM `users` WHERE `id` = ?';
    paramsInner = [row.userID];
    resultInner = await require('./kernel-db.js')(sqlInner, paramsInner, true);
    // проверка на наличие юзера, тк он бывает уже удален, а его пропуск в бд есть
    // из-за этого эксепшен на undefined.id ниже 
    // нужна целостность связи между таблицами, тк каскадное удаление по FK
    // в табл users удален user с id - сразу удалить его заявки в табл passes с этим userID
    if (resultInner.length) { 
      // поиск по квартирам
      let sqlInnerIssuer, paramsInnerIssuer, resultInnerIssuer, flatList, fromAptConcat;
      sqlInnerIssuer = 'SELECT * FROM `accounts` WHERE `userID` = ?';
      paramsInnerIssuer = resultInner[0].type === 'main' ? [resultInner[0].id] : [resultInner[0].mainUserID];
      resultInnerIssuer = await require('./kernel-db.js')(sqlInnerIssuer, paramsInnerIssuer, true);
      fromAptConcat = ''; // сюда склеим квартиры или мм

      // пока убираем фильтры по типу ЛС и добавляем все ЛС юзера том порядке как они идут в БД
      // TODO отсортировать список помещений в порядке типов appartment->auto->other
      flatList = resultInnerIssuer; // массив всех ЛС соб-ка
      // flatList = resultInnerIssuer.filter( row => row.accountType === 'appartment'); // выделим ЛС типа квартир
      // if (!flatList.length) flatList = resultInnerIssuer.filter( row => row.accountType === 'auto'); // если нет кв, то м/м
      // if (!flatList.length) flatList = resultInnerIssuer.filter( row => row.accountType === 'other'); // если нет кв, то оставшиеся типы
      fromAptConcat = flatList.reduce( (prev, current) => prev + current.apptNumberFull + "; ", ""); //  склеим № кв
      row.issuer = {
        "accountID": resultInner[0].id, // на самом деле id юзера, чтобы не менять поле на клиенте АРМ
        "accountType": resultInner[0].type,
        "address": "Мытищи", // пока захардокить
        "number": fromAptConcat, // пока захардокить
      };
    };
  
    // апдейтим статус всех заявок со статусом "open" на "delivered"
    if (row.status === "open") { 
      // console.log(`АРМ запросил заявку № ${row.id} - апдейтим статус на "delivered"`);
      let sql, params, result;
      sql = 'UPDATE passes SET status=?, dateDelivered=? WHERE id=?';
      params = [ENV.PassStatusDelivered, new Date(), row.id];
      result = await require('./kernel-db.js')(sql, params, true);
      // TODO если в result вернется rowsAffected = 0 то залогировать ошибку

      // Уведомить пушами на все ус-ва юзера 
      // 2. Получим userID юзера к которому привязан пропуск
      sql = 'SELECT userID FROM `passes` WHERE id=?';
      params = [row.id];
      result = await require('./kernel-db.js')(sql, params, true);
      // 3. Получим tel юзера c найденным userID
      sql = 'SELECT tel FROM `users` WHERE id=?';
      params = [result[0].userID];
      result = await require('./kernel-db.js')(sql, params, true);
      // 4. Получим пуштокены юзера c найденным tel
      sql = 'SELECT pushtoken FROM `user_sessions` WHERE userid=?';
      params = [result[0].tel];
      result = await require('./kernel-db.js')(sql, params, true);
      
      // отправим пуши на все найденные токены ус-в
      console.log('Пуштокены юзера: ', result);
      let message = 'принята';
      for (const rowPush of result) {
        resultPush = await require('./kernel-push.js')(
          {
            "pushToken": rowPush.pushtoken,
            "title": "Смена статуса заявки на пропуск.",
            "body": `Ваша заявка ${message}`
          }
        );
      }
    };
    
    // console.log("row: ", row);
    passList.push(row);
  }

  console.log("getPassListARM вернул пропусков: ", passList.length);
  return passList;
}

// получить список новостей
exports.getNews = async (userRequest) => {
  // userRequest ПУСТОЙ
  console.log('inside getNews:', userRequest);
  let sql, params, result;

  sql = 'SELECT * FROM `news`';
  params = ['']; // потом вообще убрать
  result = await require('./kernel-db.js')(sql, params);

  // преобразу формат всех таймстемпов
  // for (const [i, row] of result.entries()) {
  //   result[i].timestamp = result[i].timestamp ? formatISOTimeStamp(result[i].timestamp) : result[i].timestamp;
  // };
  
  return { data: 
    { news: result }
   };
}

// получить новость
exports.getNewsArticle = async (userRequest) => {
  // userRequest = id новости
  console.log('inside getNewsArticle:', userRequest);
  let sql, params, result;

  sql = 'SELECT * FROM `news` WHERE id = ?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);

  // преобразу формат всех таймстемпов
  // for (const [i, row] of result.entries()) {
  //   result[i].created = result[i].created ? formatISOTimeStamp(result[i].created) : result[i].created;
  //   result[i].activated = result[i].activated ? formatISOTimeStamp(result[i].activated) : result[i].activated;
  //   result[i].dpaSignedDate = result[i].dpaSignedDate ? formatISOTimeStamp(result[i].dpaSignedDate) : result[i].dpaSignedDate;
  //   // преобразуем поле linkedUserPermissions из строки в массив у всех найденных линкедов
  //   result[i].linkedUserPermissions = result[i].linkedUserPermissions ?
  //     row.linkedUserPermissions.split(',') : [];
  // };
  
  return { data: result[0] };
}


exports.updatePass = async userRequest => {
  // userRequest = {id, data}
  // обновление заявки пропуска, пока только поле favorite
  console.log('inside updatePass с параметром:', userRequest);
  
  let sql, params, result;

  // проверяем наличие поля favorite - если есть, то апдейтим его
  if (userRequest.data.favorite !== undefined) {
    sql = 'UPDATE passes SET favorite=? WHERE id=?';
    params = [userRequest.data.favorite, userRequest.id];
    result = await require('./kernel-db.js')(sql, params);
    
    if (result.affectedRows !== 1) {
      return { error: { message: `Пропуск c id = ${userRequest.id} не найден в базе`}};
    };
  };

  // проверяем наличие поля status - если есть, то апдейтим его
  if (userRequest.data.status !== undefined) {
    // вставь сюда кусок из updatePassStatus
  };

  // если ок, возвращаем полную модель пропуска
  sql = 'SELECT * FROM passes WHERE id=?';
  params = [userRequest.id];
  result = await require('./kernel-db.js')(sql, params);
  // форматируем все поля даты в респонсе перед отдачей клиенту
  result[0].dateCreated = formatISOTimeStamp(result[0].dateCreated);
  result[0].dateClosed = result[0].dateClosed ? formatISOTimeStamp(result[0].dateClosed) : null;
  result[0].dateDelivered = result[0].dateDelivered ? formatISOTimeStamp(result[0].dateDelivered) : null;
  result[0].dateCreatedCvs = result[0].dateCreatedCvs ? formatISOTimeStamp(result[0].dateDelivered) : null;
  result[0].specialNumberFormat = result[0].specialNumberFormat ? true : false;
  result[0].favorite = result[0].favorite ? true : false;

  return result[0];
};


exports.getDash = async userRequest => {
  // userRequest = global.user
  console.log('inside getDash с параметром:', userRequest);
  
  // инициализируем пустой объект dash и начинаем собирать данные 
  const dash = {};
  dash.info = {};

  // display_name
  sqlUser = 'SELECT * FROM users WHERE id = ?';
  paramsUser = [userRequest.id]; // потом вообще убрать
  resultUser = await require('./kernel-db.js')(sqlUser, paramsUser);

  // собираем ФИО без null
  resultUser[0].chosenSurName = resultUser[0].chosenSurName || '';
  resultUser[0].chosenName = resultUser[0].chosenName || '';
  resultUser[0].chosenMiddleName = resultUser[0].chosenMiddleName || '';
  dash.info.display_name = resultUser[0].display_name || `${resultUser[0].chosenName} ${resultUser[0].chosenMiddleName} ${resultUser[0].chosenSurName[0]}.`;

  // собираем инфу о ЛС долгах и счетчиках, в зав-сти от типа юзера и его разрешений
  // если текущий юзер - мейн или линкед с доступом к ЛС
  if (userRequest.type === ENV.UserTypeMain || (userRequest.type === ENV.UserTypeLinked && userRequest.linkedUserPermissions.includes(ENV.SectionAccounts))) {

    // если юзер мейн, то запрос ЛС по его id, а если линкед -- то по id его мейна
    const mainUserID = userRequest.type === ENV.UserTypeMain ? userRequest.id : userRequest.mainUserID;

    // получим лицевые счета (соб-ка, если мейн или мейна, если линкед)
    sqlAccounts = 'SELECT id, bitrixOuterCode AS number, bitrixOuterCode2 AS number2, accountType as type, addressConcat AS address, apptNumberFull as name FROM `accounts` WHERE `userID` = ?';
    paramsAccounts = [mainUserID];
    resultAccounts = await require('./kernel-db.js')(sqlAccounts, paramsAccounts);

    // проходя по списку ЛС добавляем по каждому общий долг и дату посл показаний счетчиков
    for (const [index, row] of resultAccounts.entries()) {
      // для текущего ЛС считаем сумму долга по квитанциям, и кол-во этих квитанций
      // sqlDebt = 'SELECT dolg_out as debt FROM `dolgi` WHERE `account_number1` = ? AND `account_number2` = ?';
      sqlDebt = 'SELECT SUM(dolg_out) as debt FROM `dolgi` WHERE `account_number1` = ? AND `account_number2` =? GROUP BY period_month, name_usl';
      paramsDebt = [resultAccounts[index].number, resultAccounts[index].number2];
      resultDebt = await require('./kernel-db.js')(sqlDebt, paramsDebt);
      console.log("resultDebt: ", resultDebt);
      const accountDebt = resultDebt.reduce( (acc, val) => acc + Number(val.debt), 0);
      resultAccounts[index].debt = { 
				"total_sum": formatMysqlDecimalToCur(accountDebt), 
				"count": resultDebt.filter( rowDebt => rowDebt.debt > 0 ).length, // кол-во ненулевых квитанций
			};
      // resultAccounts[index].debt_account_prompt = `на 14.11.24`;
      resultAccounts[index].debt_account_prompt = `${accountDebt<0?"Переплата":"Задолженность"} на ${ENV.dolgi_date_kit}`;
      // TODO result[index].number заменить Кв на Квартира, Мм на Машиноместо, Оф на Офис, Пп на Помещение
      resultAccounts[index].number = `л/с: ${resultAccounts[index].number}`;
      
      // добавляем в карточку ЛС данные по счетчикам, если это квартира И (мейн или линкед с доступом к счетчикам)
      if (resultAccounts[index].type === 'appartment' && (userRequest.type === ENV.UserTypeMain || (userRequest.type === ENV.UserTypeLinked && userRequest.linkedUserPermissions.includes(ENV.SectionMeters)))) {

        // получаем даты все даты сдачи показаний по счетчикам ЛС юзера
        sqlWMLastDates = 'SELECT last_readings_date FROM `meters_1c` WHERE account_number1 IN (SELECT bitrixOuterCode FROM accounts WHERE id = ?) AND account_number2 IN (SELECT bitrixOuterCode2 FROM accounts WHERE id = ?)';
        paramsWMLastDates = [resultAccounts[index].id, resultAccounts[index].id];
        resultWMLastDates = await require('./kernel-db.js')(sqlWMLastDates, paramsWMLastDates);
        console.log("resultWMLastDates:", resultWMLastDates);
        // если счетчики для ЛС в выгрузке 1с найдены, добавить в ответ поле meters c датой
        if (resultWMLastDates.length) {
          resultAccounts[index].meters = {
            // todo -- найти самую позднюю дату в массиве, пока возвращаем тупо от первого счетчика
            // todo -- добавить поиск в тч по meters_history среди уже отправленных на бэк, но еще не в 1с
            "last_readings": resultWMLastDates[0].last_readings_date.substring(0,10)
          };
        };
      };
    };

    // добавляем получившийся массив лицевых счетов как поле accounts в модель ответа
    dash.accounts = resultAccounts; 

    // считаем общий долг по всем ЛС
    sqlDebtTotal = 'SELECT SUM(dolg_out) as debt FROM `dolgi` WHERE account_number1 IN (SELECT bitrixOuterCode FROM accounts WHERE userID = ?) AND account_number2 IN (SELECT bitrixOuterCode2 FROM accounts WHERE userID = ?)';
    paramsDebtTotal = [mainUserID, mainUserID];
    resultDebtTotal = await require('./kernel-db.js')(sqlDebtTotal, paramsDebtTotal);

    // добавляем общий долг в модель ответа
    dash.info.debt = {
      "total": formatMysqlDecimalToCur(resultDebtTotal[0].debt)
    };
  
  };

  // получаем новости
  sqlNews = 'SELECT id, timestamp AS date, title FROM `news` ORDER BY id ASC LIMIT 5';
  paramsNews = [];
  resultNews = await require('./kernel-db.js')(sqlNews, paramsNews);

  // добавляем новости в модель ответа
  dash.news = resultNews;

  // top_services
  dash.info.top_services = [];

  // пушим раздел пропусков
  dash.info.top_services.push({"type": "passes", "has_updates": false});
  
  // пушим раздел счетчиков только если есть доступ
  if (userRequest.type === 'main' || userRequest.linkedUserPermissions.includes('meters')) {
    dash.info.top_services.push({"type": "meters", "has_updates": false});
  }
  
  // пушим раздел заявок
  // dash.info.top_services.push({"type": "services", "has_updates": false});
 
  // orders оставил для совместимости, iOS вылетает
  // dash.info.top_services.push({"type": "orders", "has_updates": false});
  
  // пушим раздел контакты
  // dash.info.top_services.push({"type": "contacts", "has_updates": false});
  
  // добавляем DPA в модель ответа
  dash.dpaSignedDate = resultUser[0].dpaSignedDate ? formatISOTimeStamp(resultUser[0].dpaSignedDate) : resultUser[0].dpaSignedDate;

  // TODO сформаировать массив разрешений по аналогии с getUserData -- пока запрашивают его отдельно
  const hasAccessTo = [];

  return dash;
};

// получить общую статистику
exports.get_stats = async (userRequest) => {
  // userRequest = ''
  console.log('inside get_stats:', userRequest);
  let sql, params, result;
  const stats = []

  // собственников всего
  sql = "SELECT count(*) AS count FROM `users` WHERE type='main' AND dpaSignedDate IS NOT NULL";
  params = [];
  result = await require('./kernel-db.js')(sql, params);
  stats.push({label:'Все собственники:', count: result[0].count});
  
  // собственников общей группы
  // sql = "SELECT count(*) AS count FROM `users` WHERE type='main' AND focusGroup IS NULL AND dpaSignedDate IS NOT NULL";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Собственники общей группы:', count: result[0].count});

  // собственников фокус-группы
  // sql = "SELECT count(*) AS count FROM `users` WHERE type='main' AND focusGroup=1 AND dpaSignedDate IS NOT NULL";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Собственники фокус-группы:', count: result[0].count});

  // линкедов общее кол-во
  sql = "SELECT count(*) AS count FROM users WHERE type = 'linked'";
  params = [];
  result = await require('./kernel-db.js')(sql, params);
  stats.push({label:'Линкедов общее кол-во:', count: result[0].count});
  
  // линкедов общее кол-во, созданных НЕ фокус группой
  // sql = "SELECT count(*) AS count FROM users WHERE type='linked' AND mainUserID IN (SELECT id from users WHERE focusGroup IS NULL)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Линкедов общее кол-во, созданных НЕ фокус группой:', count: result[0].count});
  
  // линкедов общее кол-во, созданных фокус группой
  // sql = "SELECT count(*) AS count FROM users WHERE type='linked' AND mainUserID IN (SELECT id from users WHERE focusGroup = 1)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Линкедов общее кол-во, созданных фокус группой:', count: result[0].count});
  
  // линкедов активировано
  sql = "SELECT count(*) AS count FROM users WHERE type = 'linked' AND dpaSignedDate IS NOT NULL";
  params = [];
  result = await require('./kernel-db.js')(sql, params);
  stats.push({label:'Линкедов активировано:', count: result[0].count});

  // линкедов активировано, созданных НЕ фокус группой
  // sql = "SELECT count(*) AS count FROM users WHERE type='linked' AND dpaSignedDate IS NOT NULL AND mainUserID IN (SELECT id from users WHERE focusGroup IS NULL)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Линкедов активировано, созданных НЕ фокус группой:', count: result[0].count});

  // линкедов активировано, созданных фокус группой
  // sql = "SELECT count(*) AS count FROM users WHERE type='linked' AND dpaSignedDate IS NOT NULL AND mainUserID IN (SELECT id from users WHERE focusGroup IS NOT NULL)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Линкедов активировано, созданных фокус группой:', count: result[0].count});

  // транспорта личного всего
  sql = "SELECT count(*) AS count FROM `cars`";
  params = [];
  result = await require('./kernel-db.js')(sql, params);
  stats.push({label:'Транспорта личного всего:', count: result[0].count});

  // транспорта личного НЕ фокус-группы
  // sql = "SELECT count(*) AS count FROM `cars` WHERE userID IN (SELECT id FROM users WHERE users.focusGroup IS NULL)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Транспорта личного НЕ фокус-группы:', count: result[0].count});

  // транспорта личного фокус-группы
  // sql = "SELECT count(*) AS count FROM `cars` WHERE userID IN (SELECT id FROM users WHERE users.focusGroup = 1)";
  // params = [];
  // result = await require('./kernel-db.js')(sql, params);
  // stats.push({label:'Транспорта личного фокус-группы:', count: result[0].count});


  return stats;
};

exports.updateMeter = async userRequest => {
  // userRequest = {id, data}
  // обновление имени счетчика
  console.log('inside updateMeter с параметром:', userRequest);
  
  let sqlWMUpdate, paramsWMUpdate, resultWMUpdate;

  // проверяем наличие поля name - если есть, то апдейтим его
  if (userRequest.data.name !== undefined) {
    sqlWMUpdate = 'UPDATE meters_1c SET name=? WHERE id=?';
    paramsWMUpdate = [userRequest.data.name, userRequest.id];
    resultWMUpdate = await require('./kernel-db.js')(sqlWMUpdate, paramsWMUpdate);
    
    if (resultWMUpdate.affectedRows !== 1) {
      return { error: { message: `Счетчик c id = ${userRequest.id} не найден в базе`}};
    };
  };

  // если ок, возвращаем полную модель счетчика
  let sqlWM, paramsWM, resultWM;
  sqlWM = 'SELECT * FROM meters_1c WHERE id=?';
  paramsWM = [userRequest.id];
  resultWM = await require('./kernel-db.js')(sqlWM, paramsWM);

  resultWMOut = {};
  // собираем старую модель счетчика из новой модели таблицы
  resultWMOut.id = resultWM[0].id;
  resultWMOut.bitrixID = 0;
  resultWMOut.accountID = 0; // todo починить! проверить влияет ли отсутсвие id лс на логику
  resultWMOut.name = resultWM[0].name || resultWM[0].name_1c; // если name пустой то берем из name_1c
  resultWMOut.number = resultWM[0].number_manufacture;
  resultWMOut.type = resultWM[0].name_1c.includes('ХВС') ? "cold" : "hot"; // todo: учесть ещё teplo
  resultWMOut.serialCode = resultWM[0].serial_code_1c;
  
  // приведем дату поверки к формату YYYY-MM-DD 00:00:00
  const d = resultWM[0].check_date.substring(0, 10).split('.');
  resultWMOut.nextCheckDate = `${d[2]}-${d[1]}-${d[0]} 00:00:00`;

  resultWMOut.serviceType = resultWM[0].service_type;
  resultWMOut.integers = 5;
  resultWMOut.decimals = 2;

  // поиск в таблице meters_history непереданные в 1с показания
  let last_readings_date, last_readings_value, last_readings_status;
  sqlWMHistory = 'SELECT * FROM `meters_history` WHERE `unitedID` = ? AND `number` = ? AND `uploaded1c` IS NULL ORDER BY ts DESC LIMIT 1';
  paramsWMHistory = [resultWM[0].account_number1, resultWM[0].number_manufacture];
  resultWMHistory = await require('./kernel-db.js')(sqlWMHistory, paramsWMHistory);
  // если есть непереданные показания то отдаем их с флагом 
  if (resultWMHistory.length && resultWMHistory[0].ts) {
    last_readings_date = formatISOTimeStamp(resultWMHistory[0].ts);
    last_readings_value = ((parseFloat(resultWMHistory[0].value)).toString()).replace('.',',');
    last_readings_status = 1;
  } else {
    // приведем дату после показаний к формату YYYY-MM-DD 00:00:00
    const dd = resultWM[0].last_readings_date.substring(0, 10).split('.');
    last_readings_date = `${dd[2]}-${dd[1]}-${dd[0]} 00:00:00`;
    last_readings_value = resultWM[0].last_readings_value;
    last_readings_status = 0;
  };
  // добавляем последние показания в модель счетчика
  resultWMOut.lastValue = {
    "date": last_readings_date ,
    "value": last_readings_value ,
    "status": last_readings_status 
  };

  return resultWMOut;
};


// методы функционала семья

exports.getInvites = async userRequest => {
  // userRequest = global.user
  console.log('inside getUsers:', userRequest);
  let sqlInvitesIn, paramsInvitesIn, resultInvitesIn;
  let sqlInvitesOut, paramsInvitesOut, resultInvitesOut;
  let resultInvites = {};

  // 1а) найти в таблице access_user_assignments запись с текущим юзером в роли "Админ группы" и вытащить группу
  // 1б) найти в таблице groups запись c ownerId = текущий юзер и вытащить группу
  // 2) найти в таблице access_user_assignments записи с группой из 1 запроса и с ролью НЕ админ
  // 3) в модель ответа каждого юзера добавить роль из таблицы access_user_assignments 
  // 4) в модель ответа каждого юзера добавить тип из users (main/linked) и ФИО из 3х полей
  // 5) в модель ответа каждого юзера добавить стейт инвайта чтобы показать иконкой (ожидает или принят)

  // ВТОРАЯ РЕДАКЦИЯ МЕТОДА получения списка юзеров КАК GET /invites
  // то есть по сути меняем список юзеров со статусом их инвайтов 
  // на список самих инвайтов с соб-ными статусами и приложенными к ним user_type
  // для получения списка инвайтов текущего юзера
  // 

  // запросить входящие инвайты
  sqlInvitesIn = 'SELECT * FROM invites WHERE invitedUserId=? AND status="pending"';
  paramsInvitesIn = [userRequest.id];
  resultInvitesIn = await require('./kernel-db.js')(sqlInvitesIn, paramsInvitesIn);
  console.log("Массив входящих инвайтов", resultInvitesIn);
  let sqlInvitesInUser, paramsInvitesInUser, resultInvitesInUser;
  let sqlInvitesInRole, paramsInvitesInRole, resultInvitesInRole;
  for (const [indexInvitesIn, rowInvitesIn] of resultInvitesIn.entries()) {
    // добавим в каждый входящий инвайт инфу о юзере инициаторе этого инвайта
    sqlInvitesInUser = "SELECT * FROM users WHERE id = ?";
    paramsInvitesInUser = [resultInvitesIn[indexInvitesIn].initiatorId];
    resultInvitesInUser = await require('./kernel-db.js')(sqlInvitesInUser, paramsInvitesInUser);
    // проверка на всякий случай
    if (!resultInvitesInUser.length) {
      return { 
        "error" : 
        {"message": `В инвайте ${resultInvitesIn[indexInvitesIn].id} указан id отсуствующего юзера ${resultInvitesIn[indexInvitesIn].invitedUserId}`},
      }
    };
    resultInvitesIn[indexInvitesIn].user = {};
    resultInvitesIn[indexInvitesIn].user.id = resultInvitesInUser[0].id;
    resultInvitesIn[indexInvitesIn].user.type = resultInvitesInUser[0].type;
    resultInvitesIn[indexInvitesIn].user.tel = resultInvitesInUser[0].tel;
    resultInvitesIn[indexInvitesIn].user.chosenSurName = resultInvitesInUser[0].chosenSurName;
    resultInvitesIn[indexInvitesIn].user.chosenName = resultInvitesInUser[0].chosenName;
    resultInvitesIn[indexInvitesIn].user.chosenMiddleName = resultInvitesInUser[0].chosenMiddleName;
    // добавим в каждый входящий инвайт инфу о роли с которой инциатор пригласил текущего юзера
    sqlInvitesInRole = "SELECT * FROM access_roles WHERE id = ?";
    paramsInvitesInRole = [resultInvitesIn[indexInvitesIn].roleId];
    resultInvitesInRole = await require('./kernel-db.js')(sqlInvitesInRole, paramsInvitesInRole);
    resultInvitesIn[indexInvitesIn].role = {};
    resultInvitesIn[indexInvitesIn].role.id = resultInvitesInRole[0].id;
    resultInvitesIn[indexInvitesIn].role.title = resultInvitesInRole[0].title;
    resultInvitesIn[indexInvitesIn].role.description = resultInvitesInRole[0].description;
  };

  resultInvites.incoming = resultInvitesIn;
  
  // запросить исходящие инвайты
  sqlInvitesOut = 'SELECT * FROM invites WHERE initiatorId=? AND (status="pending" OR status="accepted")';
  paramsInvitesOut = [userRequest.id];
  resultInvitesOut = await require('./kernel-db.js')(sqlInvitesOut, paramsInvitesOut);
  console.log("Массив исходящих инвайтов", resultInvitesOut);
  let sqlInviteOutUser, paramsInviteOutUser, resultInvitesOutUser;
  let sqlInvitesOutRole, paramsInvitesOutRole, resultInvitesOutRole;
  for (const [indexInvitesOut, rowInvitesOut] of resultInvitesOut.entries()) {
    // добавим в каждый исходящий инвайт инфу о приглашенном юзере
    sqlInviteOutUser = "SELECT * FROM users WHERE id = ?";
    paramsInviteOutUser = [resultInvitesOut[indexInvitesOut].invitedUserId];
    resultInvitesOutUser = await require('./kernel-db.js')(sqlInviteOutUser, paramsInviteOutUser);
    // проверка на всякий случай
    if (!resultInvitesOutUser.length) {
      return { 
        "error" : 
        {"message": `В исходящем инвайте ${resultInvitesOut[indexInvitesOut].id} указан id отсуствующего юзера ${resultInvitesOut[indexInvitesOut].invitedUserId}`},
      }
    };
    resultInvitesOut[indexInvitesOut].user = {};
    resultInvitesOut[indexInvitesOut].user.id = resultInvitesOutUser[0].id;
    resultInvitesOut[indexInvitesOut].user.type = resultInvitesOutUser[0].type;
    resultInvitesOut[indexInvitesOut].user.tel = resultInvitesOutUser[0].tel;
    resultInvitesOut[indexInvitesOut].user.chosenSurName = resultInvitesOutUser[0].chosenSurName;
    resultInvitesOut[indexInvitesOut].user.chosenName = resultInvitesOutUser[0].chosenName;
    resultInvitesOut[indexInvitesOut].user.chosenMiddleName = resultInvitesOutUser[0].chosenMiddleName;
    // добавим в каждый исходящий инвайт инфу о роли приглашенного юзера
    sqlInvitesOutRole = "SELECT * FROM access_roles WHERE id = ?";
    paramsInvitesOutRole = [resultInvitesOut[indexInvitesOut].roleId];
    resultInvitesOutRole = await require('./kernel-db.js')(sqlInvitesOutRole, paramsInvitesOutRole);
    resultInvitesOut[indexInvitesOut].role = {};
    resultInvitesOut[indexInvitesOut].role.id = resultInvitesOutRole[0].id;
    resultInvitesOut[indexInvitesOut].role.title = resultInvitesOutRole[0].title;
    resultInvitesOut[indexInvitesOut].role.description = resultInvitesOutRole[0].description;
  };

  resultInvites.outgoing = resultInvitesOut;

  return resultInvites;
};

exports.getInviteById = async userRequest => {
  // userRequest = {...req.params, ...{globalUser: global.user}}
  console.log('inside getUsersById:', userRequest.id);

  // ГАРД проверка наличия id инвайта во вх параметрах
  if (!userRequest.id) {
    return { 
      "error" : 
      {"message": `Во входящих параметрах отсутствует id инвайта.`}
    }
  };
  
  // запросить инвайт
  sqlInvite = 'SELECT * FROM invites WHERE id=?';
  paramsInvite = [userRequest.id];
  resultInvite = await require('./kernel-db.js')(sqlInvite, paramsInvite);
  console.log("Полученный инвайт: ", resultInvite);
  
  // ГАРД если инвайт не найдем по id
  if (!resultInvite.length) {
    return { 
      "error" : 
      {"message": `Не найден инвайт [${userRequest.id}]`}
    }
  };

  // ГАРД запрашивать инфу об инвайте может только юзеры из полей initiatorId и 
  if (resultInvite[0].initiatorId !== userRequest.globalUser.id && resultInvite[0].invitedUserId  !== userRequest.globalUser.id) {
    return { 
      "error" : 
      {"message": `У юзера [${userRequest.globalUser.id}] tel [${userRequest.globalUser.tel}] недостаточно прав для просмотра данных инвайта [${userRequest.id}].`}
    }
  };

  let sqlInviteUser, paramsInviteUser, resultInviteUser;
  let sqlInviteRole, paramsInviteRole, resultInviteRole;
  for (const [indexInvitesOut, rowInvitesOut] of resultInvite.entries()) {
    // добавим в инвайт инфу о приглашенном юзере
    sqlInviteUser = "SELECT * FROM users WHERE id = ?";
    paramsInviteUser = [resultInvite[indexInvitesOut].invitedUserId];
    resultInviteUser = await require('./kernel-db.js')(sqlInviteUser, paramsInviteUser);
    resultInvite[indexInvitesOut].user = {};
    resultInvite[indexInvitesOut].user.id = resultInviteUser[0].id;
    resultInvite[indexInvitesOut].user.type = resultInviteUser[0].type;
    resultInvite[indexInvitesOut].user.tel = resultInviteUser[0].tel;
    resultInvite[indexInvitesOut].user.chosenSurName = resultInviteUser[0].chosenSurName;
    resultInvite[indexInvitesOut].user.chosenName = resultInviteUser[0].chosenName;
    resultInvite[indexInvitesOut].user.chosenMiddleName = resultInviteUser[0].chosenMiddleName;
    // добавим в инвайт инфу о роли приглашенного юзера
    sqlInviteRole = "SELECT * FROM access_roles WHERE id = ?";
    paramsInviteRole = [resultInvite[indexInvitesOut].roleId];
    resultInviteRole = await require('./kernel-db.js')(sqlInviteRole, paramsInviteRole);
    resultInvite[indexInvitesOut].role = {};
    resultInvite[indexInvitesOut].role.id = resultInviteRole[0].id;
    resultInvite[indexInvitesOut].role.title = resultInviteRole[0].title;
    resultInvite[indexInvitesOut].role.description = resultInviteRole[0].description;
  };

  return resultInvite[0];
};

exports.patchInviteById = async userRequest => {
  // userRequest = {...req.params, ...req.body, globalUser: global.user}
  console.log('inside patchInviteById:', userRequest);

  // ГАРД находим инвайт -- если его нет - 422
  let sql, params, result;
  sqlInvite = 'SELECT * FROM invites WHERE id=?';
  paramsInvite = [userRequest.id];
  resultInvite = await require('./kernel-db.js')(sqlInvite, paramsInvite);
  
  if (!resultInvite.length) {
    return { 
      "error" : 
      {"message": `No invite found with id ${userRequest.id}`}
    }
  };

  // ГАРД патчить данные инвайта может только его инициатор
  if (userRequest.globalUser.id !== resultInvite[0].initiatorId) {
    return { 
      "error" : 
      {"message": `У вас нет доступа к изменению параметров приглашения [${userRequest.id}]. Только инициатору инвайта доступны эти действия.`}
    }
  };

  // ГАРД патчить данные можно только у активного инвайта в статусах pending / accepted
  if (resultInvite[0].status !== 'pending' && resultInvite[0].status !== 'accepted') {
    return { 
      "error" : 
      {"message": `Нельзя изменить параметры у неактивного инвайта`}
    }
  };
  
  // определяем тип юзера в инвайте мейн/линкед для понимания нужно ли апдейтить табл users
  let sqlInvitedUser, paramsInvitedUser, resultInvitedUser;
  sqlInvitedUser = 'SELECT * FROM users WHERE id=?';
  paramsInvitedUser = [resultInvite[0].invitedUserId];
  resultInvitedUser = await require('./kernel-db.js')(sqlInvitedUser, paramsInvitedUser);
  
  if (!resultInvitedUser.length) {
    return { 
      "error" : 
      {"message": `No invited user found with id ${resultInvite[0].invitedUserId} in invite with id ${userRequest.id}`}
    }
  };

  // TODO если передан параметр roleId - проверить что эта роль системная или пренадлежит мейну от которого запрос
  // пока поставил проверку на то что роль с таким айдишником вообще существует (но может быть и чужой)
  if (userRequest.roleId) {
    let sqlRole, paramsRole, resultRole;
    sqlRole = 'SELECT * FROM access_roles WHERE id=?';
    paramsRole = [userRequest.roleId];
    resultRole = await require('./kernel-db.js')(sqlRole, paramsRole);
    
    if (!resultRole.length) {
      return { 
        "error" : 
        {"message": `No role found with id = ${userRequest.roleId}`}
      }
    };
  }

  // TODO дописать апдейт поля updateAt на now или поставь триггер в MySQL на апдейт
  
  // массив всех полей пришедших в запросе
  console.log("userRequest", userRequest);
  const reqFields = Object.keys(userRequest);

  // массив разрешенных к апдейту полей инвайта
  const canBeUpdatedFieldsInvite = [
    {reqFieldName: "roleId", tableFieldName: "roleId"},
  ];

  // массив полей который пришел с клиента 
  const toUpdateFieldsInvite = canBeUpdatedFieldsInvite.filter ( field => reqFields.includes(field.reqFieldName) );
  console.log("массив полей, пришедших в запросе на обновление", toUpdateFieldsInvite);
  
  // формируем SQL-запрос, склеиваем результирующую строку запроса
  // нужно включить на апдейт только те поля, которые есть в userRequest и только из разрешенного списка
  sql = toUpdateFieldsInvite.reduce ( (prev, current) => prev + current.tableFieldName + ' = ?,', '');
  sql = sql.slice(0, -1); // удяляем посл запятую https://stackoverflow.com/a/952945/6056120
  sql = `UPDATE invites SET ${sql} WHERE id = ?`; // формируем окончательный вид строки запроса
  console.log("строка SQL к апдейту: ", sql);
  
  // cформируем массив подстановочных значений вместо ?
  params = toUpdateFieldsInvite.map( field => userRequest[field.reqFieldName]);
  // .. и добавим к нему последний параметр для WHERE id = ?
  params.push(userRequest.id);
  console.log("params к апдейту: ", params);
  
  result = await require('./kernel-db.js')(sql, params);
  
  // при успешном апдейте mysql возвращает объект с полями
  // соотв-но по полю "affectedRows": 1 контроллер в server-db.js примет решение след действия
  // {
  //   "fieldCount": 0,
  //   "affectedRows": 1,
  //   "insertId": 0,
  //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
  //   "serverStatus": 2,
  //   "warningStatus": 0,
  //   "changedRows": 1
  // }

  // если юзер линкед то апдейтим прилетевшие поля ФИО
  if (resultInvitedUser[0].type ==='linked') {

    // массив разрешенных к апдейту полей линкеда
    const canBeUpdatedFieldsUser = [
      {reqFieldName: "chosenName", tableFieldName: "chosenName"},
      {reqFieldName: "chosenMiddleName", tableFieldName: "chosenMiddleName"},
      {reqFieldName: "chosenSurName", tableFieldName: "chosenSurName"},
    ];

    // массив полей который пришел с клиента 
    const toUpdateFieldsUser = canBeUpdatedFieldsUser.filter ( field => reqFields.includes(field.reqFieldName) );
    console.log("массив полей, пришедших в запросе на обновление", toUpdateFieldsUser);
    
    // формируем SQL-запрос, склеиваем результирующую строку запроса
    // нужно включить на апдейт только те поля, которые есть в userRequest и только из разрешенного списка
    sql = toUpdateFieldsUser.reduce ( (prev, current) => prev + current.tableFieldName + ' = ?,', '');
    sql = sql.slice(0, -1); // удяляем посл запятую https://stackoverflow.com/a/952945/6056120
    sql = `UPDATE users SET ${sql} WHERE id = ?`; // формируем окончательный вид строки запроса
    console.log("строка SQL к апдейту: ", sql);
    
    // cформируем массив подстановочных значений вместо ?
    params = toUpdateFieldsUser.map( field => userRequest[field.reqFieldName]);
    // .. и добавим к нему последний параметр для WHERE id = ?
    params.push(resultInvitedUser[0].id);
    console.log("params к апдейту: ", params);
    
    result = await require('./kernel-db.js')(sql, params);

};

  return result;
};

exports.patchInviteByAction = async userRequest => {
  // userRequest = {...req.params, action: 'accept', globalUser: global.user}
  console.log('inside patchInviteByAction:', userRequest);

  // ГАРД проверяем что инвайт есть в системе
  let sqlInvite, paramsInvite, resultInvite;
  sqlInvite = 'SELECT * FROM invites WHERE id=?';
  paramsInvite = [userRequest.id];
  resultInvite = await require('./kernel-db.js')(sqlInvite, paramsInvite);
  
  if (!resultInvite.length) {
    return { 
      "error" : 
      {"message": `No invite found with id ${userRequest.id}`}
    }
  };
  
  // ГАРД: находим инвайтед юзера из инвайта и проверям что он есть в системе 
  let sqlInvitedUser, paramsInvitedUser, resultInvitedUser;
  sqlInvitedUser = 'SELECT * FROM users WHERE id=?';
  paramsInvitedUser = [resultInvite[0].invitedUserId];
  resultInvitedUser = await require('./kernel-db.js')(sqlInvitedUser, paramsInvitedUser);
  
  if (!resultInvitedUser.length) {
    return { 
      "error" : 
      {"message": `No invited user found with id ${resultInvite[0].invitedUserId} in invite with id ${userRequest.id}`}
    }
  };

  // ГАРД: находим инициатора из инвайта и проверям что он есть в системе
  let sqlInitiatorUser, paramsInitiatorUser, resultInitiatorUser;
  sqlInitiatorUser = 'SELECT * FROM users WHERE id=?';
  paramsInitiatorUser = [resultInvite[0].initiatorId];
  resultInitiatorUser = await require('./kernel-db.js')(sqlInitiatorUser, paramsInitiatorUser);
  
  if (!resultInitiatorUser.length) {
    return { 
      "error" : 
      {"message": `No initiator user found with id ${resultInvite[0].invitedUserId} in invite with id ${userRequest.id}`}
    }
  };


  // TODO проверить что юзер, отправивший экшен, имеет право совершения операций над переданным инвайтом

  if (userRequest.action === 'accept') {

    // ГАРД: проверить что текущий юзер имеет право совершать данное действие на данным инвайтом
    // принять приглашение может только приглашенный
    if (userRequest.globalUser.id !== resultInvite[0].invitedUserId) {
      return { 
        "error" : 
        {"message": `Вы не можете совершать действие [${userRequest.action}] над инвайтом [${resultInvite[0].id}]`}
      }
    };
    
    // ГАРД: проверить что над данным инвайтом можно совершить указанный экшен
    // принять приглашение можно только в текущем статусе инвайта pending
    if (resultInvite[0].status !== 'pending') {
      return { 
        "error" : 
        {"message": `Действие [${userRequest.action}] недопустимо над инвайтом в статусе [${resultInvite[0].status}]`}
      }
    };

    // добавляем запись экшена в invites_actions
    let sqlAddInviteAction, paramsAddInviteAction, resultAddInviteAction;
    sqlAddInviteAction = 'INSERT INTO invites_actions (initiatorId, invitedUserId, action, sentByUserId, roleId) VALUES (?,?,?,?,?)';
    paramsAddInviteAction = [resultInvite[0].initiatorId, resultInvite[0].invitedUserId, userRequest.action, userRequest.globalUser.id, resultInvite[0].roleId];
    resultAddInviteAction = await require('./kernel-db.js')(sqlAddInviteAction, paramsAddInviteAction);

    // апдейтим полученный в начале модуля инвайт, меняем ему статус на accepted
    let sqlInviteUpdate, paramsInviteUpdate, resultInviteUpdate;
    sqlInviteUpdate = `UPDATE invites SET status = ?, lastAction = ?, lastActionById = ? WHERE id = ?`;
    paramsInviteUpdate = ['accepted', userRequest.action, userRequest.globalUser.id, resultInvite[0].id];
    resultInviteUpdate = await require('./kernel-db.js')(sqlInviteUpdate, paramsInviteUpdate);
    
    // при успешном апдейте mysql возвращает объект с полями
    // {
    //   "fieldCount": 0,
    //   "affectedRows": 1,
    //   "insertId": 0,
    //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
    //   "serverStatus": 2,
    //   "warningStatus": 0,
    //   "changedRows": 1
    // }

    // ГАРД: проверяем результат апдейта и выкидываем ошибку если что
    if (resultInviteUpdate.affectedRows !== 1) {
      return { 
        "error" : 
        {"message": `Couldn't update invite with id ${resultInvite[0].id} on action ${userRequest.action}`}
      }
    };

    return "Модуль patchInviteByAction отработал экшен accept";  
  };

  if (userRequest.action === 'reject') {

    // ГАРД: проверить что текущий юзер имеет право совершать данное действие на данным инвайтом
    // отказаться от приглашения может только приглашенный
    if (userRequest.globalUser.id !== resultInvite[0].invitedUserId) {
      return { 
        "error" : 
        {"message": `Вы не можете совершать действие [${userRequest.action}] над инвайтом [${resultInvite[0].id}]`}
      }
    };

    // ГАРД: проверить что над данным инвайтом можно совершить указанный экшен
    // отказаться от приглашения можно только в текущем статусе инвайта pending
    if (resultInvite[0].status !== 'pending') {
      return { 
        "error" : 
        {"message": `Действие [${userRequest.action}] недопустимо над инвайтом в статусе [${resultInvite[0].status}]`}
      }
    };

    // добавляем запись экшена в invites_actions
    let sqlAddInviteAction, paramsAddInviteAction, resultAddInviteAction;
    sqlAddInviteAction = 'INSERT INTO invites_actions (initiatorId, invitedUserId, action, sentByUserId, roleId) VALUES (?,?,?,?,?)';
    paramsAddInviteAction = [resultInvite[0].initiatorId, resultInvite[0].invitedUserId, userRequest.action, userRequest.globalUser.id, resultInvite[0].roleId];
    resultAddInviteAction = await require('./kernel-db.js')(sqlAddInviteAction, paramsAddInviteAction);

    // апдейтим полученный в начале модуля инвайт, меняем ему статус на rejected
    let sqlInviteUpdate, paramsInviteUpdate, resultInviteUpdate;
    sqlInviteUpdate = `UPDATE invites SET status = ?, lastAction = ?, lastActionById = ? WHERE id = ?`;
    paramsInviteUpdate = ['rejected', userRequest.action, userRequest.globalUser.id, resultInvite[0].id];
    resultInviteUpdate = await require('./kernel-db.js')(sqlInviteUpdate, paramsInviteUpdate);
    
    // при успешном апдейте mysql возвращает объект с полями
    // {
    //   "fieldCount": 0,
    //   "affectedRows": 1,
    //   "insertId": 0,
    //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
    //   "serverStatus": 2,
    //   "warningStatus": 0,
    //   "changedRows": 1
    // }

    // ГАРД: проверяем результат апдейта и выкидываем ошибку если что
    if (resultInviteUpdate.affectedRows !== 1) {
      return { 
        "error" : 
        {"message": `Couldn't update invite with id ${resultInvite[0].id} on action ${userRequest.action}`}
      }
    };

    return "Модуль patchInviteByAction отработал экшен reject";  
  };

  if (userRequest.action === 'cancel') {

    // ГАРД: проверить что текущий юзер имеет право совершать данное действие на данным инвайтом
    // выйти из группы может только приглашенный
    if (userRequest.globalUser.id !== resultInvite[0].invitedUserId) {
      return { 
        "error" : 
        {"message": `Вы не можете совершать действие [${userRequest.action}] над инвайтом [${resultInvite[0].id}]`}
      }
    };

    // ГАРД: проверить что над инвайтом можно совершить указанный экшен
    // выйти из группы можно только в текущем статусе инвайта accepted
    if (resultInvite[0].status !== 'accepted') {
      return { 
        "error" : 
        {"message": `Действие [${userRequest.action}] недопустимо над инвайтом в статусе [${resultInvite[0].status}]`}
      }
    };

    // добавляем запись экшена в invites_actions
    let sqlAddInviteAction, paramsAddInviteAction, resultAddInviteAction;
    sqlAddInviteAction = 'INSERT INTO invites_actions (initiatorId, invitedUserId, action, sentByUserId, roleId) VALUES (?,?,?,?,?)';
    paramsAddInviteAction = [resultInvite[0].initiatorId, resultInvite[0].invitedUserId, userRequest.action, userRequest.globalUser.id, resultInvite[0].roleId];
    resultAddInviteAction = await require('./kernel-db.js')(sqlAddInviteAction, paramsAddInviteAction);

    // апдейтим полученный в начале модуля инвайт, меняем ему статус на canceled
    let sqlInviteUpdate, paramsInviteUpdate, resultInviteUpdate;
    sqlInviteUpdate = `UPDATE invites SET status = ?, lastAction = ?, lastActionById = ? WHERE id = ?`;
    paramsInviteUpdate = ['canceled', userRequest.action, userRequest.globalUser.id, resultInvite[0].id];
    resultInviteUpdate = await require('./kernel-db.js')(sqlInviteUpdate, paramsInviteUpdate);
    
    // при успешном апдейте mysql возвращает объект с полями
    // {
    //   "fieldCount": 0,
    //   "affectedRows": 1,
    //   "insertId": 0,
    //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
    //   "serverStatus": 2,
    //   "warningStatus": 0,
    //   "changedRows": 1
    // }

    // ГАРД: проверяем результат апдейта и выкидываем ошибку если что
    if (resultInviteUpdate.affectedRows !== 1) {
      return { 
        "error" : 
        {"message": `Couldn't update invite with id ${resultInvite[0].id} on action ${userRequest.action}`}
      }
    };

    return "Модуль patchInviteByAction отработал экшен cancel";  
  };

  if (userRequest.action === 'exclude') {

    // ГАРД: проверить что текущий юзер имеет право совершать данное действие на данным инвайтом
    // исключить из группы может только инициатор инвайта
    if (userRequest.globalUser.id !== resultInvite[0].initiatorId) {
      return { 
        "error" : 
        {"message": `Вы не можете совершать действие [${userRequest.action}] над инвайтом [${resultInvite[0].id}]`}
      }
    };

    // ГАРД: проверить что над данным инвайтом можно совершить указанный экшен
    // исключение из группы возможно только в текущем статусе инвайта pending или accepted
    if (resultInvite[0].status !== 'pending' && resultInvite[0].status !== 'accepted') {
      return { 
        "error" : 
        {"message": `Действие [${userRequest.action}] недопустимо над инвайтом в статусе [${resultInvite[0].status}]`}
      }
    };

    // добавляем запись экшена в invites_actions
    let sqlAddInviteAction, paramsAddInviteAction, resultAddInviteAction;
    sqlAddInviteAction = 'INSERT INTO invites_actions (initiatorId, invitedUserId, action, sentByUserId, roleId) VALUES (?,?,?,?,?)';
    paramsAddInviteAction = [resultInvite[0].initiatorId, resultInvite[0].invitedUserId, userRequest.action, userRequest.globalUser.id, resultInvite[0].roleId];
    resultAddInviteAction = await require('./kernel-db.js')(sqlAddInviteAction, paramsAddInviteAction);

    // апдейтим полученный в начале модуля инвайт, меняем ему статус на excluded
    let sqlInviteUpdate, paramsInviteUpdate, resultInviteUpdate;
    sqlInviteUpdate = `UPDATE invites SET status = ?, lastAction = ?, lastActionById = ? WHERE id = ?`;
    paramsInviteUpdate = ['excluded', userRequest.action, userRequest.globalUser.id, resultInvite[0].id];
    resultInviteUpdate = await require('./kernel-db.js')(sqlInviteUpdate, paramsInviteUpdate);
    
    // при успешном апдейте mysql возвращает объект с полями
    // {
    //   "fieldCount": 0,
    //   "affectedRows": 1,
    //   "insertId": 0,
    //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
    //   "serverStatus": 2,
    //   "warningStatus": 0,
    //   "changedRows": 1
    // }

    // ГАРД - проверяем результат апдейта и выкидываем ошибку если что
    if (resultInviteUpdate.affectedRows !== 1) {
      return { 
        "error" : 
        {"message": `Couldn't update invite with id ${resultInvite[0].id} on action ${userRequest.action}`}
      }
    };

    return "Модуль patchInviteByAction отработал экшен exclude";
  };

  
  return "Модуль patchInviteByAction отработал";
};


exports.deleteInviteById = async userRequest => {
  // userRequest = req.params
  console.log('inside deleteInviteById:', userRequest);
  let sql, params, result;

  // метод пока выключен, тк не имеет применения. Все патчи инвайтов - через енпоинты экшенов
  return { 
    "error" : 
    {"message": `Метод деактивирован. Для изменения статусов приглашений используются PATCH-ендпоинты с экшенами.`}
  };

  // TODO если деактивирутся линкед, то проверить что он пренадлежит текущему мейну

  // в случае удаления инвайта к мейну -- просто записываем в экшены и меняем статус
  // для линкеда нужно предусмотеть софт-делит вместо физического удаления из users

  // апдейт инвайта 

  sql = `UPDATE invites SET status=?, lastAction=?, updatedAt=? WHERE id = ?`; //
  params = ['revoked', 'revoked', new Date(), userRequest.id];
  result = await require('./kernel-db.js')(sql, params);

  // 

  // sql = 'DELETE FROM `users` WHERE id=? AND type=?'; // type=ENV.UserTypeLinked страховка, чтобы случайно не удалить main
  // params = [userRequest.id, ENV.UserTypeLinked];
  // result = await require('./kernel-db.js')(sql, params);

  return result;
};

exports.getInviteUserChectByTel = async userRequest => {
  // userRequest = {globalUser: global.user, newInvite: req.params}
  console.log('inside getInviteUserChectByTel:', userRequest);
  console.log('userRequest.newInvite.tel:', userRequest.newInvite.tel);

  // выносим сюда из роутера проверку отсутсвия параметра tel в запросе юзера
  if (!userRequest.newInvite.tel) {
    return { 
      "error" : 
      {"message": "Missing parameter tel"}
    }
  };

  let sqlUser, paramsUser, resultUser;
  let sqlInvites, paramsInvites, resultInvites;

  // ищем приглашенного в users по номеру тел
  sqlUser = `SELECT * FROM users WHERE tel = ?`; //
  paramsUser = [userRequest.newInvite.tel];
  resultUser = await require('./kernel-db.js')(sqlUser, paramsUser);

  // ГАРД на наличие номера тел в users  -- если не найден, можно создать приглос на линкеда
  if (!resultUser.length) {
    return {
      "invitable": 1,
      "type": 'linked',
      "prompt": {
        "title" : `Пользователь доступен для приглашения`,
        "description" : `Пользователя с номером ${userRequest.newInvite.tel} еще нет в системе. Ему можно выслать приглашение.`
      },
    }
  };

  // ГАРД на запрет приглашения самого себя
  if (userRequest.globalUser.tel === userRequest.newInvite.tel) {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Вы не можете добавить в группу самого себя.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Вы не можете отправить приглашение самому себе.`
      }
    }
  };

  // ГАРД на запрет приглашения линкеда с уже имеющимся активным статусом инвайта
  sqlInvites = `SELECT * FROM invites WHERE invitedUserId = ? AND status = ?`;
  paramsInvites = [resultUser[0].id, 'accepted'];
  resultInvites = await require('./kernel-db.js')(sqlInvites, paramsInvites);
  if (resultInvites.length && resultUser[0].type === 'linked') {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
      }
    }
  };

  // ГАРД на запрет приглашения линкеда с уже имеющимся приглашением в статусе pending (те множественных вх приглашений)
  // множестенные вх приглашения для линкедов запрещаем, тк нет логики их отработки в интерфейсе
  sqlInvites = `SELECT * FROM invites WHERE invitedUserId = ? AND status = ?`;
  paramsInvites = [resultUser[0].id, 'pending'];
  resultInvites = await require('./kernel-db.js')(sqlInvites, paramsInvites);
  if (resultInvites.length && resultUser[0].type === 'linked') {
    console.log("Тест resultInvites", resultInvites);
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователь с номером ${userRequest.newInvite.tel} уже приглашен другим пользователем.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователь с номером ${userRequest.newInvite.tel} уже приглашен другим пользователем.`
      }
    }
  };

  // ГАРД на запрет приглашения мейна-админа группы, в которой есть активные инвайты к другим мейнам
  sqlInvites = `SELECT * FROM users WHERE id IN (SELECT invitedUserId FROM invites WHERE initiatorId = ? AND status = ?) AND type = ?`;
  paramsInvites = [resultUser[0].id, 'accepted', 'main'];
  resultInvites = await require('./kernel-db.js')(sqlInvites, paramsInvites);
  if (resultInvites.length) {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
      }
    }
  };
  
  // ГАРД на запрет приглашения мейна, приглашенного в другую группу, в активном статусе инвайта
  sqlInvites = `SELECT * FROM users WHERE id IN (SELECT initiatorId FROM invites WHERE invitedUserId = ? AND status = ?) AND type = ?`;
  paramsInvites = [resultUser[0].id, 'accepted', 'main'];
  resultInvites = await require('./kernel-db.js')(sqlInvites, paramsInvites);
  if (resultInvites.length) {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в другой группе.`
      }
    }
  };
  
  // ГАРД на запрет приглашения юзера, который уже есть среди инвайтов текущего юзера в любом статусе, кроме завершенных
  sqlInvites = `SELECT * FROM invites WHERE initiatorId = ? AND invitedUserId = ?`;
  paramsInvites = [userRequest.globalUser.id, resultUser[0].id];
  resultInvites = await require('./kernel-db.js')(sqlInvites, paramsInvites);
  if (resultInvites.length && resultInvites[0].status === 'pending') {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователю с номером ${userRequest.newInvite.tel} уже отправлено приглашение от вас.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователю с номером ${userRequest.newInvite.tel} уже отправлено приглашение от вас.`
      }
    }
  };
  if (resultInvites.length && resultInvites[0].status === 'accepted') {
    // return {
    //   "invitable": 0,
    //   "type": resultUser[0].type,
    //   "prompt": {
    //     "title" : `Не удалось добавить пользователя`,
    //     "description" : `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в вашей группе.`
    //   },
    // };
    return { 
      "error" : {
        "title": `Не удалось добавить пользователя`,
        "message": `Пользователь с номером ${userRequest.newInvite.tel} уже состоит в вашей группе.`
      }
    }
  };
  

  // КОНЕЦ ГАРДОВ
  // после всех гардов и разрешенного приглоса лиинкеда, остался только вариант мейна, доступного к приглашению
  return {
    "invitable": 1,
    "type": resultUser[0].type,
    "prompt": {
      "title" : `Пользователь доступен для приглашения`,
      "description" : `Пользователь с номером ${userRequest.newInvite.tel} есть в системе. Ему можно выслать приглашение.`
    },
  };
};

exports.postInviteCreate = async userRequest => {
  // userRequest = {globalUser: global.user, check: check} -- текущий юзер и рез-т проверки телефона другим модулем
  console.log('inside postInvateCreate:', userRequest);
  let sqlRoles, paramsRoles, resultRoles;

  // return userRequest; // проверим что поступило на вход

  // ГАРД доп проверка модуля чека, можно удалить, тк в случае invitable=0 модуль чека сам выкинет ошибку выше по стеку вызовов
  if (!userRequest.check.invitable) return {
    "error" : {
      "title": `Не удалось добавить пользователя.`,
      "message": `Какая-то ошибка, ранее не пойманная в модуле проверки инвайтов.`
    },
  };

  // Для рендера списка ролей на клиенте получим доступные текущему юзеру роли (его + системные)
  sqlRoles = `SELECT * FROM access_roles WHERE ownerId = ? OR ownerId = ?`;
  paramsRoles = [userRequest.globalUser.id, 0];
  resultRoles = await require('./kernel-db.js')(sqlRoles, paramsRoles);

  const inviteModel = {};
  // добавим в модель нового инвайта массив полученных пролей 
  inviteModel.roles = resultRoles;
  // добавим в модель нового инвайта тип юзера чтобы на клиенте поняли показывать ли поля ФИО
  inviteModel.type = userRequest.check.type;

  return inviteModel;
};

exports.postInvite = async userRequest => {
  // userRequest = {globalUser: global.user, newInvite: req.body, check: check} -- данные нового инвайта и рез-т проверки телефона другим модулем
  console.log('inside postInvite:', userRequest);

  // return userRequest; // проверим что поступило на вход

  // ГАРД доп проверка модуля чека, можно удалить, тк в случае invitable=0 модуль чека сам выкинет ошибку выше по стеку вызовов
  if (!userRequest.check.invitable) return {
    "error" : {
      "title": `Не удалось добавить пользователя.`,
      "message": `Какая-то ошибка, ранее не пойманная в модуле проверки инвайтов.`
    },
  };

  let invitedUserId;

  // если приглашаемый - линкед, если его еще нет, нужно его создать и получить его айдишник
  if (userRequest.check.type === 'linked') {
    let sqlGetUser, paramsGetUser, resultGetUser;
    sqlGetUser = `SELECT * FROM users WHERE tel = ?`;
    paramsGetUser = [userRequest.newInvite.tel,];
    resultGetUser = await require('./kernel-db.js')(sqlGetUser, paramsGetUser);
    
    if (resultGetUser.length) {
      // если линкед с этим тел уже есть в системе, то возвращаем его айдишник
      invitedUserId = resultGetUser[0].id;
    } else {
      // если линкеда с этим тел нет в системе, то создавем и возвращаем его айдишник
      // замена отсутсвующего параметра на null чтобы избежать ошибки при INSERT
      const chosenName = userRequest.newInvite.chosenName !== undefined ? userRequest.newInvite.chosenName : null;
      const chosenMiddleName = userRequest.newInvite.chosenMiddleName !== undefined ? userRequest.newInvite.chosenMiddleName : null;
      const chosenSurName = userRequest.newInvite.chosenSurName !== undefined ? userRequest.newInvite.chosenSurName : null;

      let sqlCreateUser, paramsCreateUser, resultCreateUser;
      sqlCreateUser = 'INSERT INTO `users` (mainUserID, tel, chosenName, chosenMiddleName, chosenSurName, info, type, linkedUserPermissions) values (?, ?, ?, ?, ?, ?, ?, ?)';
      paramsCreateUser = [
        userRequest.globalUser.id,
        userRequest.newInvite.tel,
        chosenName,
        chosenMiddleName,
        chosenSurName,
        '',
        ENV.UserTypeLinked,
        userRequest.linkedUserPermissions ? userRequest.linkedUserPermissions : ""
      ];
      resultCreateUser = await require('./kernel-db.js')(sqlCreateUser, paramsCreateUser);
      
      if (resultCreateUser.affectedRows !== 1) {
        return { error: { message: "Пользователь не добавился в базу"}};
      }

      // берем айдишник только что созданной записи
      invitedUserId = resultCreateUser.insertId;
    };
  };

  // если приглашаемый - мейн, берем его айдишник из users по тел
  if (userRequest.check.type === 'main') {
    let sqlGetUser, paramsGetUser, resultGetUser;
    sqlGetUser = `SELECT * FROM users WHERE tel = ?`;
    paramsGetUser = [userRequest.newInvite.tel,];
    resultGetUser = await require('./kernel-db.js')(sqlGetUser, paramsGetUser);
    
    invitedUserId = resultGetUser[0].id;
  };

  // добавляем запись экшена в invites_actions
  let sqlAddInviteAction, paramsAddInviteAction, resultAddInviteAction;
  sqlAddInviteAction = 'INSERT INTO invites_actions (initiatorId, invitedUserId, action, sentByUserId, roleId) VALUES (?,?,?,?,?)';
  paramsAddInviteAction = [userRequest.globalUser.id, invitedUserId, 'create', userRequest.globalUser.id, userRequest.newInvite.roleId];
  resultAddInviteAction = await require('./kernel-db.js')(sqlAddInviteAction, paramsAddInviteAction);

  // добавляем или апдейтим запись в invites
  // для этого находим есть ли уже запись в invites с указанными initiatorId, invitedUserId
  let sqlGetInvite, paramsGetInvite, resultGetInvite;
  sqlGetInvite = `SELECT * FROM invites WHERE initiatorId = ? AND invitedUserId = ?`;
  paramsGetInvite = [userRequest.globalUser.id, invitedUserId];
  resultGetInvite = await require('./kernel-db.js')(sqlGetInvite, paramsGetInvite);

  let newInviteId; // присвоим этой переменной айдишник созданной или проапдейченнй записи инвайта в табл invites

  if (!resultGetInvite.length) {
    // запись в invites с initiatorId, invitedUserId не найдена -- добавляем новую
    let sqlCreateInvite, paramsCreateInvite, resultCreateInvite;
    sqlCreateInvite = `INSERT INTO invites (initiatorId, invitedUserId, roleId, status, lastAction, lastActionById) VALUES (?,?,?,?,?,?)`;
    paramsCreateInvite = [
      userRequest.globalUser.id,
      invitedUserId,
      userRequest.newInvite.roleId,
      'pending',
      'created',
      userRequest.globalUser.id
    ];
    resultCreateInvite = await require('./kernel-db.js')(sqlCreateInvite, paramsCreateInvite);
    // при успешном инсерте mysql возвращает объект с полями, из него можно взять insertId
    // {
    //   fieldCount: 0,
    //   affectedRows: 1,
    //   insertId: 44,
    //   info: '',
    //   serverStatus: 2,
    //   warningStatus: 0
    // }

    console.log ("resultCreateInvite", resultCreateInvite);
    if (resultCreateInvite.insertId) newInviteId = resultCreateInvite.insertId;
    
    // добавляем запись в invites причем если между данными юзерами уже был неактивный инвайт, то запись апдейтится
    // отказался от ON DUPLICATE KEY UPDATE из-за невозможности вернуть id проапдейченной записи в отличие от INSERT
    // sqlCreateInvite = `INSERT INTO invites (initiatorId, invitedUserId, roleId, status, lastAction) VALUES (?,?,?,?,?)
    //                     ON DUPLICATE KEY UPDATE
    //                     roleId = ?, status = ?, lastAction = ?`;
    // paramsCreateInvite = [
    //   userRequest.globalUser.id,
    //   invitedUserId,
    //   userRequest.newInvite.roleId,
    //   'pending',
    //   'created',
    //   userRequest.newInvite.roleId,
    //   'pending',
    //   'created'
    // ];

    // сохраним полученный айдишник созданный записи

  } else {
    // запись в invites с initiatorId, invitedUserId найдена -- апдейтим ее по её айдишнику
    let sqlInviteUpdate, paramsInviteUpdate, resultInviteUpdate;
    sqlInviteUpdate = `UPDATE invites SET roleId = ?, status = ?, lastAction = ? WHERE id = ?`;
    paramsInviteUpdate = [
      userRequest.newInvite.roleId,
      'pending',
      'created',
      resultGetInvite[0].id
    ];
    resultInviteUpdate = await require('./kernel-db.js')(sqlInviteUpdate, paramsInviteUpdate);
    // при успешном апдейте mysql возвращает объект с полями
    // {
    //   "fieldCount": 0,
    //   "affectedRows": 1,
    //   "insertId": 0,
    //   "info": "Rows matched: 1  Changed: 1  Warnings: 0",
    //   "serverStatus": 2,
    //   "warningStatus": 0,
    //   "changedRows": 1
    // }
    // при успешном апдейте возвращаем айдишник ранее найденного инвайта, который мы апдейтим
    if (resultInviteUpdate.affectedRows) newInviteId = resultGetInvite[0].id;
  };

  if (newInviteId) return {inviteId: newInviteId};

  // если ни инсерта ни апдейта не произошло -- выкидываем 422
  return { 
    "error" : {
      "title": `Не удалось создать инвайт`,
      "message": `Не произошло добавления/обновления записи в invites при создании инвайта`
    }
  }
};

exports.getRoles = async userRequest => {
  // userRequest = {globalUser: global.user}
  console.log('inside getRoles:', userRequest);

  // ГАРД доп проверка на наличие id юзера в userRequest
  if (!userRequest.globalUser.id) return {
    "error" : {
      "message": `В запросе списка ролей отсутствует id пользователя globalUser`
    },
  };

  let sqlGetRoles, paramsGetRoles, resultGetRoles;
  // запросить роли текущего юзера + системные
  sqlGetRoles = 'SELECT * FROM access_roles WHERE ownerId = ? OR ownerId = ?';
  paramsGetRoles = [0, userRequest.globalUser.id];
  resultGetRoles = await require('./kernel-db.js')(sqlGetRoles, paramsGetRoles);
  console.log("Массив ролей", resultGetRoles);

  return resultGetRoles;
};

exports.getRolesById = async userRequest => {
  // userRequest = {...req.params, ...{globalUser: global.user}}
  console.log('inside getRolesById:', userRequest);

  // ГАРД на наличие id глобал юзера в userRequest
  if (!userRequest.globalUser.id) return {
    "error" : {
      "message": `В запросе роли отсутствует id пользователя globalUser`
    },
  };

  // ГАРД на наличие id роли в userRequest
  if (!userRequest.id) return {
    "error" : {
      "message": `В запросе роли отсутствует id роли`
    },
  };

  // вытаскиваем роль по ее id
  let sqlGetRoleById, paramsGetRoleById, resultGetRoleById;
  sqlGetRoleById = 'SELECT * FROM access_roles WHERE id = ?';
  paramsGetRoleById = [userRequest.id];
  resultGetRoleById = await require('./kernel-db.js')(sqlGetRoleById, paramsGetRoleById);
  console.log("Роль:", resultGetRoleById);

  // ГАРД на длину массива результата запроса
  if (!resultGetRoleById.length) return {
    "error" : {
      "message": `Не найдена роль [${userRequest.id}]`
    },
  };
  
  // ГАРД права на просмотра роли по ownerId
  if (resultGetRoleById[0].ownerId !== userRequest.globalUser.id && resultGetRoleById[0].ownerId !== 0) return {
    "error" : {
      "message": `У вас нет прав на просмотр роли [${userRequest.id}]`
    },
  };

  // формируем модель структуры роли по группам пермишензов 
  const roleViewModel = {
    permissionGroups: [
      {
        "permissionGroupTitle" : "Лицевые счета",
        "permissionItems" : [1, 2],
      },
      {
        "permissionGroupTitle" : "Счетчики",
        "permissionItems" : [3, 4],
      },
    ],
  };


  // версия 1 -- возвращаем массив пермишензов отдельно от объекта roleViewModel
  // // получаем пермишензы данной роли
  // let sqlRolePermissions, paramsRolePermissions, resultRolePermissions;
  // sqlRolePermissions = 'SELECT * FROM access_roles_permissions WHERE roleId = ?';
  // paramsRolePermissions = [userRequest.id];
  // resultRolePermissions = await require('./kernel-db.js')(sqlRolePermissions, paramsRolePermissions);
  // console.log("Пермишенз для роли:", resultRolePermissions);

  // // ГАРД на наличие пермишнзов в указанной роли
  // if (!resultRolePermissions.length) return {
  //   "error" : {
  //     "message": `Не найдены пермишензы для роли [${userRequest.id}]`
  //   },
  // };

  // // идем по массиву полученных пермишензов указанной роли
  // for (const [indexRolePermissions, rowRolePermissions] of resultRolePermissions.entries()) {
  //   // получаем описание пермишенза и массив зависимых пермишензов из таблицы access_permissions
  //   let sqlPermission, paramsPermission, resultPermission;
  //   sqlPermission = 'SELECT * FROM access_permissions WHERE id = ?';
  //   paramsPermission = [rowRolePermissions.permissionId];
  //   resultPermission = await require('./kernel-db.js')(sqlPermission, paramsPermission);
  //   console.log("Системный пермишенз из таблицы access_permissions:", resultPermission);

  //   // ГАРД на существование указанного системного пермишнза
  //   if (!resultPermission.length) return {
  //     "error" : {
  //       "message": `Не найден системный пермишенз [${rowPermissionItem}]`
  //     },
  //   };

  //   resultRolePermissions[indexRolePermissions].id = rowRolePermissions.id;
  //   resultRolePermissions[indexRolePermissions].systemPermissionId = resultPermission[0].id;
  //   resultRolePermissions[indexRolePermissions].description = resultPermission[0].description;

  // };
  
  // return {
    //   "roleViewModel": roleViewModel,
    //   "rolePermissions" : resultRolePermissions
    // };


  // версия 2 -- возвращаем пермишензы одним объектом структуры модельки, внутри групп, много SQL-запросов в цикле
  // // идем по модели структуры роли и заполняем в соотв-вии с ней результирующий объект resutlRole
  // const resutlRole = {};
  // resutlRole.permissionGroups = [];
  // let permissionGroup, permissionItem;
  // for (const [indexPermissionGroup, rowPermissionGroup] of roleViewModel.permissionGroups.entries()) {
  //   permissionGroup = {};
  //   permissionGroup.title = rowPermissionGroup.permissionGroupTitle;
  //   permissionGroup.permissionItems = [];
  //   for (const [indexPermissionItem, rowPermissionItem] of rowPermissionGroup.permissionItems.entries()) {
  //     // получаем данные пермишенза данной роли
  //     let sqlRolePermission, paramsRolePermission, resultRolePermission;
  //     sqlRolePermission = 'SELECT * FROM access_roles_permissions WHERE roleId = ? AND permissionId = ?';
  //     paramsRolePermission = [userRequest.id, rowPermissionItem];
  //     resultRolePermission = await require('./kernel-db.js')(sqlRolePermission, paramsRolePermission);
  //     console.log("Пермишенз для роли:", resultRolePermission);

  //     // ГАРД на существование указанного пермишнза в указанной роли
  //     if (!resultRolePermission.length) return {
  //       "error" : {
  //         "message": `Не найден пермишенз [${rowPermissionItem}] для роли [${userRequest.id}]`
  //       },
  //     };

  //     // ГАРД на существование более одной записи указанного пермишенза для данной роли
  //     if (resultRolePermission.length > 1) return {
  //       "error" : {
  //         "message": `Для роли [${userRequest.id}] найдено более одного пермишенза [${rowPermissionItem}] `
  //       },
  //     };

  //     // получаем описание пермишенза и массив зависимых пермишензов из таблицы access_permissions
  //     let sqlPermission, paramsPermission, resultPermission;
  //     sqlPermission = 'SELECT * FROM access_permissions WHERE id = ?';
  //     paramsPermission = [resultRolePermission[0].permissionId];
  //     resultPermission = await require('./kernel-db.js')(sqlPermission, paramsPermission);
  //     console.log("Системный пермишенз из таблицы access_permissions:", resultPermission);

  //     // ГАРД на существование указанного системного пермишнза
  //     if (!resultPermission.length) return {
  //       "error" : {
  //         "message": `Не найден системный пермишенз [${rowPermissionItem}]`
  //       },
  //     };

  //     permissionItem = {};
  //     permissionItem.id = resultRolePermission[0].id;
  //     // permissionItem.systemPermissionId = resultPermission[0].id;
  //     permissionItem.description = resultPermission[0].description;
  //     permissionItem.value = resultRolePermission[0].value;
  //     // переводим в массив int-ов строку со списком айдишников, которые нужно включить, в случае включения данного пермишенза
  //     const switchOnArray = resultPermission[0].switchOn ? resultPermission[0].switchOn.split(',').map(el=>parseInt(el)) : [];
  //     // вместо айдишников системных пермишензов подставляем айдишники этих пермишензов в текущей роли
  //     permissionItem.switchOn = switchOnArray;
  //     permissionItem.switchOn = switchOnArray.map( el => {
  //       console.log("INSIDE FILTER", resultRolePermission.filter( permission => permission.permissionId === el ));
  //       return (resultRolePermission.filter( permission => permission.permissionId === el)).id
  //     });
      
  //     // переводим в массив int-ов строку со списком айдишников, которые нужно выключить, в случае выключения данного пермишенза
  //     permissionItem.switchOff = resultPermission[0].switchOff ? resultPermission[0].switchOff.split(',').map(el=>parseInt(el)) : [];

  //     // пушим полученный объект пермишенза в массив permissionGroup.permissionItems
  //     permissionGroup.permissionItems.push(permissionItem);
  //   };

  //   // пушим полученный объект permissionGroup в массив resutlRole.permissionGroups
  //   resutlRole.permissionGroups.push(permissionGroup);
  // }


  // версия 3 -- возвращаем пермишензы одним объектом структуры модельки, внутри групп, два SQL-запроса, фильттрация методами JS
  // идем по модели структуры роли и заполняем в соотв-вии с ней результирующий объект resutlRole
  const resutlRole = {};
  resutlRole.permissionGroups = [];
  let permissionGroup, permissionItem;

  // получим все пермишензы привязанные к роли
  let sqlRolePermissions, paramsRolePermissions, resultRolePermissions;
  sqlRolePermissions = 'SELECT * FROM access_roles_permissions WHERE roleId = ?';
  paramsRolePermissions = [userRequest.id];
  resultRolePermissions = await require('./kernel-db.js')(sqlRolePermissions, paramsRolePermissions);
  console.log("Пермишензы для роли:", resultRolePermissions);
  
  // получим все системные пермишензы привязанные к пермишензам роли
  let sqlRoleSystemPermissions, paramsRoleSystemPermissions, resultRoleSystemPermissions;
  sqlRoleSystemPermissions = 'SELECT * FROM access_permissions WHERE id IN (SELECT permissionId FROM access_roles_permissions WHERE roleId = ?)';
  paramsRoleSystemPermissions = [userRequest.id];
  resultRoleSystemPermissions = await require('./kernel-db.js')(sqlRoleSystemPermissions, paramsRoleSystemPermissions);
  console.log("Пермишензы для роли:", resultRoleSystemPermissions);

  // return {
  //   "resultRolePermissions" : resultRolePermissions,
  //   "resultRoleSystemPermissions" : resultRoleSystemPermissions,
  // }
  
  // идем по модели ответа и по айдишникам системных пермишензов подставляем в массивы permissionItems соотв-щие им пермишензы из группы
  for (const [indexPermissionGroup, rowPermissionGroup] of roleViewModel.permissionGroups.entries()) {
    permissionGroup = {};
    permissionGroup.title = rowPermissionGroup.permissionGroupTitle;
    permissionGroup.permissionItems = [];
    for (const [indexPermissionItem, rowPermissionItem] of rowPermissionGroup.permissionItems.entries()) {

      // вытащим объект группового пермишенза который ссылается на айдишник системного пермишенза rowPermissionItem
      const rolePermission = resultRolePermissions.filter (el => el.permissionId === rowPermissionItem);
      
      // ГАРД на существование ролевого пермишенза который ссылается на системный пермишенз rowPermissionItem
      if (!rolePermission.length) return {
        "error" : {
          "message": `Для роли [${userRequest.id}] не найден ролевой пермишенз, ссылающийся на системный пермишенз [${rowPermissionItem}] `
        },
      };
      
      // вытащим объект системного пермишенза по его айдишнику rowPermissionItem
      const roleSystemPermission = resultRoleSystemPermissions.filter (el => el.id === rowPermissionItem);

      // ГАРД на существование системного пермишенза rowPermissionItem
      if (!roleSystemPermission.length) return {
        "error" : {
          "message": `Не найден системный пермишенз [${rowPermissionItem}] при формаировании пермишензов роли [${userRequest.id}]`
        },
      };

      // собираем элемент пермишенза из системного (description) и группового (value)
      permissionItem = {};
      permissionItem.id = rolePermission[0].id;
      permissionItem.value = rolePermission[0].value;
      permissionItem.description = roleSystemPermission[0].description;
      // переводим в массив int-ов строку со списком айдишников, которые нужно включить, в случае включения данного пермишенза
      permissionItem.systemPermissionId = roleSystemPermission[0].id;
      const switchOnArray = roleSystemPermission[0].switchOn ? roleSystemPermission[0].switchOn.split(',').map(el=>parseInt(el)) : [];
      // переведем айдищники системных пермишензов из поля switchOn в айдишники соотв-щих им ролевых пермишензов
      // permissionItem.switchOn = switchOnArray.map ( el => resultRolePermissions.filter ( el1 => el1.permissionId === el)[0].id );
      permissionItem.systemPermissionIdSwitchOn = switchOnArray;
      // переводим в массив int-ов строку со списком айдишников, которые нужно включить, в случае включения данного пермишенза
      const switchOffArray = roleSystemPermission[0].switchOff ? roleSystemPermission[0].switchOff.split(',').map(el=>parseInt(el)) : [];
      // переведем айдищники системных пермишензов из поля switchOn в айдишники соотв-щих им ролевых пермишензов
      // permissionItem.switchOff = switchOffArray.map ( el => resultRolePermissions.filter ( el1 => el1.permissionId === el)[0].id );
      permissionItem.systemPermissionIdSwitchOff = switchOffArray;
      
      // пушим полученный объект пермишенза в массив permissionGroup.permissionItems
      permissionGroup.permissionItems.push(permissionItem);
    };

    // пушим полученный объект permissionGroup в массив resutlRole.permissionGroups
    resutlRole.permissionGroups.push(permissionGroup);
  }

  // отдаем сформированную структуру роли
  return resutlRole;
};

exports.patchRolesById = async userRequest => {
  console.log('inside patchRolesById:', userRequest);
  let sql, params, result;

  result = "Обновили роль с id " + userRequest;

  return result;
};

exports.postRolesCreate = async userRequest => {
  console.log('inside postRolesCreate:', userRequest);
  let sql, params, result;

  //ГАРД - убедиться что юзер имеет право создавать роль (мейн)
  



  result = "Вернули полную модель пустой роли для заполнения";

  return result;
};

exports.postRoles = async userRequest => {
  console.log('inside postRoles:', userRequest);
  let sql, params, result;

  result = "Создали новую роль по ранее присланной модели";

  return result;
};

exports.deleteRolesById = async userRequest => {
  console.log('inside deleteRolesById:', userRequest);
  let sql, params, result;

  result = "Удалили роль с id " + userRequest;

  return result;
};

exports.getCheckPermissionsById = async userRequest => {
  console.log('inside getCheckPermissionsById:', userRequest);
  let sql, params, result;

  result = "Вернули массив связанных пермишензов и их стейтов, согласно стейту переданного пермишенза: " + JSON.stringify(userRequest);

  return result;
};


// методы обработки запросов раздела "Заявки"

// общий список заявок текущего юзера
exports.getServices = async userRequest => {
  // userRequest = global.user
  console.log('inside getServices:', userRequest);
  
  // запросить сервисы
  let sqlServices, paramsServices, resultServices;
  sqlServices = 'SELECT * FROM services WHERE userID=? ORDER BY createdAt DESC';
  paramsServices = [userRequest.id];
  resultServices = await require('./kernel-db.js')(sqlServices, paramsServices);
  console.log("Массив всех заявок текущего юзера", resultServices);
  
  // запросить статусы
  let sqlServicesStatuses, paramsServicesStatuses, resultServicesStatuses;
  sqlServicesStatuses = 'SELECT * FROM services_statuses';
  paramsServicesStatuses = [];
  resultServicesStatuses = await require('./kernel-db.js')(sqlServicesStatuses, paramsServicesStatuses);
  console.log("Массив всех статусов", resultServicesStatuses);

  // инициализируем возвращаемый объект и массивы active / history в нем
  const resultServicesOut = {};
  resultServicesOut.active = [];
  resultServicesOut.history = [];
  
  // разложим полученные заявки по массивам active и history в зав-сти от их статуса
  for (const [indexServices, rowServices] of resultServices.entries()) {
    // инициализируем объект заявки для добавление в массив заявок
    const service = {};
    service.id = rowServices.id;
    service.number = rowServices.number;
    // service.account = `${resultServiceAccount[0].bitrixOuterCode} (${resultServiceAccount[0].apptNumberFull})`;
    service.account = `${rowServices.fixed_account_appt} (${rowServices.fixed_account_number})`;
    service.description = rowServices.description;
    service.createdAt = rowServices.createdAt ? formatISOTimeStamp(rowServices.createdAt) : null;
    // в поле status текущей заявки положим модель статуса
    const status = resultServicesStatuses.filter(el => el.id === rowServices.statusID)[0];
    service.status = {};
    service.status.id = status.id;
    service.status.name = status.name;
    service.status.title = status.title;
    service.status.style = {textColor: status.textColor, bgColor: status.bgColor} // стилизация бейджа статуса
    // добавим поле с моделью категории, со значениями полей, сохраненными в момент создания заявки
    service.category = {
      "id": 1,
      "title": rowServices.fixed_category_title || "",
      "paid": rowServices.fixed_category_paid || 0,
      "edIzm": "",
      "price": rowServices.fixed_category_price || "",
      "description": ""
    };
    
    // если статус заявки 4 или 5 (неактивный) и прошло 24ч с момента создания, то в history
    if (
        (rowServices.statusID === 4 || rowServices.statusID === 5) && 
        time_diff(new Date(rowServices.createdAt)) > 24
    ) {
      resultServicesOut.history.push(service);
    } else {
      // иначе в active
      resultServicesOut.active.push(service);
    };
  };
  
  // возвращаемый объект с массивами заявок
  return resultServicesOut;
};

// детали заявки
exports.getServiceById = async userRequest => {
  // userRequest = {...req.params, ...{globalUser: global.user}}
  console.log('inside getServiceById:', userRequest);

  // ГАРД проверка наличия id заявки во вх параметрах
  if (!userRequest.id) {
    return { 
      "error" : 
      {"message": `Во входящих параметрах отсутствует id заявки`}
    }
  };
  
  // запросить заявку
  let sqlServiceById, paramsServiceById, resultServiceById;
  sqlServiceById = 'SELECT * FROM services WHERE id=?';
  paramsServiceById = [userRequest.id];
  resultServiceById = await require('./kernel-db.js')(sqlServiceById, paramsServiceById);
  console.log("Полученная заявка: ", resultServiceById);
  
  // ГАРД если заявка не найдема по id
  if (!resultServiceById.length) {
    return { 
      "error" : 
      {"message": `Не найдена заявка [${userRequest.id}]`}
    }
  };

  // ГАРД запрашивать инфу о заявке может только юзер из поля userID этой заявки
  if (resultServiceById[0].userID !== userRequest.globalUser.id) {
    return { 
      "error" : 
      {"message": `У юзера [${userRequest.globalUser.id}] tel [${userRequest.globalUser.tel}] недостаточно прав для просмотра данных заявки [${userRequest.id}].`}
    }
  };

  // иинциализируем возвращаемый объект
  const resultServiceByIdOut = {};

    // формируем поля заявки для ответа
  resultServiceByIdOut.id = resultServiceById[0].id;
  resultServiceByIdOut.number = resultServiceById[0].number;
  resultServiceByIdOut.account = `${resultServiceById[0].fixed_account_appt} (${resultServiceById[0].fixed_account_number})`;
  resultServiceByIdOut.description = resultServiceById[0].description;
  resultServiceByIdOut.createdAt = formatISOTimeStamp(resultServiceById[0].createdAt);
  resultServiceByIdOut.closedAt = resultServiceById[0].closedAt ? formatISOTimeStamp(resultServiceById[0].closedAt) : null;
  resultServiceByIdOut.category = {
    "id": 1,
    "title": resultServiceById[0].fixed_category_title || "",
    "paid": resultServiceById[0].fixed_category_paid || 0,
    "edIzm": "",
    "price": resultServiceById[0].fixed_category_price || "",
    "description": ""
  };


  // получим массив все статусов
  let sqlServicesStatuses, paramsServicesStatuses, resultServicesStatuses;
  sqlServicesStatuses = 'SELECT * FROM services_statuses';
  paramsServicesStatuses = [];
  resultServicesStatuses = await require('./kernel-db.js')(sqlServicesStatuses, paramsServicesStatuses);
  console.log("Массив всех статусов", resultServicesStatuses);

  // обогащаем базовую модель заявки кастомными полями и добавяем их в возвращаемый объект

  // добавляем информацию по текущему статусу
  const status = resultServicesStatuses.filter(el=>el.id === resultServiceById[0].statusID)[0]
  resultServiceByIdOut.status = {};
  // формируем базовые поля статуса для ответа
  resultServiceByIdOut.status.id = status.id;
  resultServiceByIdOut.status.name = status.name; // системное название статуса на англ
  resultServiceByIdOut.status.title = status.title; // название статуса в интерфейсе
  resultServiceByIdOut.status.style = {textColor: status.textColor, bgColor: status.bgColor} // стилизация бейджа статуса
  resultServiceByIdOut.status.description = status.description; // описание статуса для юзера
  resultServiceByIdOut.status.progressMask = status.progressMask.split(''); // маска прогресс-бара массивом, возм символы 'V','T','X','O'
  // обогащаем модель текущего статуса массивом следующих возможных статусов
  resultServiceByIdOut.status.nextStatus = [];
  // формируем массив для рендера кнопок перехода из текущего статуса в следующие возможные статусы из поля nextStatusID
  if (status.nextStatusID) {
    status.nextStatusID.split(',').forEach( (id, index) => {
      const nextStatus = resultServicesStatuses.filter(el=>el.id === Number(id))[0];
      const nextStatusFields = {};
      // формируем поля следующего статуса для ответа
      nextStatusFields.id = nextStatus.id;
      nextStatusFields.name = nextStatus.name;
      nextStatusFields.buttonTitle = status.nextButtonTitle.split(',')[index];
      // пушим собранный объект nextStatusFields в массив nextStatus
      resultServiceByIdOut.status.nextStatus.push(nextStatusFields);
    });
  };
  
  // добавляе в модель заявки историю переписки
  resultServiceByIdOut.history = [];
  let sqlServiceHistory, paramsServiceHistory, resultServiceHistory;
  sqlServiceHistory = 'SELECT * FROM services_events WHERE serviceID = ? AND type = ?';
  paramsServiceHistory = [userRequest.id, 'message'];
  resultServiceHistory = await require('./kernel-db.js')(sqlServiceHistory, paramsServiceHistory);
  console.log("Чат по заявке", resultServiceHistory);
  // сформируем модель евента
  for (const [indexServiceHistory, rowServiceHistory] of resultServiceHistory.entries()) {
    const serviceHistoryEvent = {};
    serviceHistoryEvent.id = rowServiceHistory.id;
    serviceHistoryEvent.direction = rowServiceHistory.direction;
    serviceHistoryEvent.message = rowServiceHistory.message;
    serviceHistoryEvent.createdAt = formatISOTimeStamp(resultServiceHistory[indexServiceHistory].createdAt);
    // добавляем модель евента в массив истории
    resultServiceByIdOut.history.push(serviceHistoryEvent);
  };


  // добавляе в модель заявки список файлов-изображений
  resultServiceByIdOut.images = [];
  let sqlServiceImages, paramsServiceImages, resultServiceImages;
  sqlServiceImages = 'SELECT * FROM services_images WHERE serviceID = ?';
  paramsServiceImages = [userRequest.id];
  resultServiceImages = await require('./kernel-db.js')(sqlServiceImages, paramsServiceImages);
  console.log("Файлы фото по заявке", resultServiceImages);
  // сформируем урлы массива картинок, основной и иконка
  for (const [indexServiceImage, rowServiceImage] of resultServiceImages.entries()) {
    const serviceImage = {};
    // serviceImage.url = `http://127.0.0.1:${ENV.PortDevKit}/api/v2/assets/img/${rowServiceImage.filename}`;
    // serviceImage.url_thumb = `http://127.0.0.1:${ENV.PortDevKit}/api/v2/assets/img/${rowServiceImage.filename}`;
    serviceImage.url = `https://app.uk-kit.ru:51579/api/v2/assets/img/${rowServiceImage.filename}`;
    serviceImage.url_thumb = `https://app.uk-kit.ru:51579/api/v2/assets/img/${rowServiceImage.filename}`;
    // добавляем модель картинки в массив картинок
    resultServiceByIdOut.images.push(serviceImage);
  };

  // отдаем суммарный объект заявки
  return resultServiceByIdOut;
};

// создание заявки
exports.createService = async userRequest => {
  // userRequest = {globalUser: global.user, newService: req.body, images: req.files}}
  console.log('inside createService:', userRequest);

  // ГАРД проверяем наличие поля accountID в запросе
  if (!userRequest.newService.accountID) {
    return { 
      "error" : 
      {"message": `В заявке отсутствует id Лицевого счета`}
    }
  };

  // Получаем инфу по ЛС присланному в новой заявке и проверяем, что он существует
  let sqlServiceAccount, paramsServiceAccount, resultServiceAccount;
  sqlServiceAccount = 'SELECT * FROM accounts WHERE id = ?';
  paramsServiceAccount = [userRequest.newService.accountID];
  resultServiceAccount = await require('./kernel-db.js')(sqlServiceAccount, paramsServiceAccount);
  console.log("ЛС в заявке", resultServiceAccount);
  
  // ГАРД проверка что ЛС существует
  if (!resultServiceAccount.length) {
    return { 
      "error" : 
      {"message": `При создании заявки не найден лицевой счет [${userRequest.newService.accountID}]`}
    }
  };
  
  // ГАРД проверяем наличие поля serviceCategoryID в запросе
  if (!userRequest.newService.serviceCategoryID) {
    return { 
      "error" : 
      {"message": `В заявке отсутствует id категории`}
    }
  };

  // Получаем инфу по категории присланнойв новой заявке
  let sqlServiceCategory, paramsServiceCategory, resultServiceCategory;
  sqlServiceCategory = 'SELECT * FROM services_categories WHERE id = ?';
  paramsServiceCategory = [userRequest.newService.serviceCategoryID];
  resultServiceCategory = await require('./kernel-db.js')(sqlServiceCategory, paramsServiceCategory);
  console.log("категория в заявке", resultServiceCategory);
  
  // ГАРД проверяем наличие поля description в запросе
  if (!userRequest.newService.description) {
    return { 
      "error" : 
      {"message": `В заявке отсутствует описание`}
    }
  };

  // ГАРД проверка что текущий юзер владеет указанным ЛС
  if (resultServiceAccount[0].userID !== userRequest.globalUser.id) {
    return { 
      "error" : 
      {"message": `Лицевой счет [${userRequest.newService.accountID}] не пренадлежит текущему юзеру [${userRequest.globalUser.id}]`}
    }
  };

  // записываем заявку в services
  let sqlCreateService, paramsCreateService, resultCreateService;
  sqlCreateService = `INSERT INTO services (
      userID, 
      accountID, 
      serviceCategoryID, 
      description, 
      statusID, 
      fixed_account_number,
      fixed_account_appt,
      1c_personal_account_code,
      fixed_category_title,
      fixed_category_paid,
      fixed_category_price)
    VALUES 
      (?,?,?,?,?,?,?,?,?,?,?)`;
  paramsCreateService = [
    userRequest.globalUser.id,
    userRequest.newService.accountID,
    userRequest.newService.serviceCategoryID,
    userRequest.newService.description,
    1,
    resultServiceAccount[0].bitrixOuterCode,
    resultServiceAccount[0].apptNumberFull,
    resultServiceAccount[0].bitrixID, // внутренний код лс в 1С
    resultServiceCategory[0].title || 'Без категории (не найдена)', // если рубрикатор поменялся пока заявка ждала добавления
    resultServiceCategory[0].paid || 0, // если рубрикатор поменялся пока заявка ждала добавления
    resultServiceCategory[0].price || "", // если рубрикатор поменялся пока заявка ждала добавления
  ];
  resultCreateService = await require('./kernel-db.js')(sqlCreateService, paramsCreateService);

  // при успешном инсерте mysql возвращает объект с полями, из него можно взять insertId
  // {
  //   fieldCount: 0,
  //   affectedRows: 1,
  //   insertId: 44,
  //   info: '',
  //   serverStatus: 2,
  //   warningStatus: 0
  // }

  // добавим события по только что созданной заявке в services_events
  let sqlCreateServiceEvent, paramsCreateServiceEvent, resultCreateServiceEvent;
  sqlCreateServiceEvent = `INSERT INTO services_events (serviceID, type, direction, statusID, message) VALUES (?,?,?,?,?)`;
  paramsCreateServiceEvent = [
    resultCreateService.insertId,
    'status',
    'out',
    1,
    userRequest.newService.description,
  ];
  resultCreateServiceEvent = await require('./kernel-db.js')(sqlCreateServiceEvent, paramsCreateServiceEvent);
  console.log ("resultCreateServiceEvent", resultCreateServiceEvent);


  // ГАРД -- на старых запросах application/json, userRequest.images = undefined
  if (userRequest.images) {
    // добавим названия файлов изображений из userRequest.images в services_images
    let sqlCreateServiceImages, paramsCreateServiceImages, resultCreateServiceImages;
    for (const [indexServiceImage, rowServiceImage] of userRequest.images.entries()) {
      sqlCreateServiceImages = `INSERT INTO services_images (serviceID, filename) VALUES (?,?)`;
      paramsCreateServiceImages = [resultCreateService.insertId, rowServiceImage.filename];
      resultCreateServiceImages = await require('./kernel-db.js')(sqlCreateServiceImages, paramsCreateServiceImages);
      console.log ("resultCreateServiceImages", resultCreateServiceImages);
    };
  };


  console.log ("resultCreateService", resultCreateService);
  // если все ок то возвращаем айдишник созданной заявки
  if (resultCreateService.insertId) return {newServiceId: resultCreateService.insertId};
  // если кернел бд не вернул id созданной заявки -- выходим с ошибкой
  return { 
    "error" : 
    {"message": `Отсутсвует id новой заявки в ответе на create`}
  };
};

// изменение заявки
exports.patchServiceById = async userRequest => {
  // userRequest = {...req.params, ...req.body, globalUser: global.user}
  console.log('inside patchServiceById:', userRequest);

  // ГАРД запрет на любое изменение из приложения
  // return { 
  //   "error" : 
  //   {"message": `Для смены статуса заявки свяжитесь с диспетчером`}
  // };


  // ГАРД проверка наличия id заявки во вх параметрах
  if (!userRequest.id) {
    return { 
      "error" : 
      {"message": `Во входящих параметрах отсутствует id заявки`}
    }
  };

  // запросить заявку
  let sqlServiceById, paramsServiceById, resultServiceById;
  sqlServiceById = 'SELECT * FROM services WHERE id=?';
  paramsServiceById = [userRequest.id];
  resultServiceById = await require('./kernel-db.js')(sqlServiceById, paramsServiceById);
  console.log("Полученная заявка: ", resultServiceById);
  
  // ГАРД если заявка не найдена по id
  if (!resultServiceById.length) {
    return { 
      "error" : 
      {"message": `Не найдена заявка [${userRequest.id}]`}
    }
  };

  // ГАРД апдейтить инфу о заявке может только юзер из поля userID этой заявки
  if (resultServiceById[0].userID !== userRequest.globalUser.id) {
    return { 
      "error" : 
      {"message": `У юзера [${userRequest.globalUser.id}] tel [${userRequest.globalUser.tel}] недостаточно прав для изменения заявки [${userRequest.id}].`}
    }
  };

  // получим инфу о статусе текущей заявки
  let sqlServicesStatusById, paramsServicesStatusById, resultServicesStatusById;
  sqlServicesStatusById = 'SELECT * FROM services_statuses WHERE id = ?';
  paramsServicesStatusById = [resultServiceById[0].statusID];
  resultServicesStatusById = await require('./kernel-db.js')(sqlServicesStatusById, paramsServicesStatusById);
  console.log("Полная инфа о статусе указанной заявки", resultServicesStatusById);
  
  // ГАРД если статус не найден по id
  if (!resultServicesStatusById.length) {
    return { 
      "error" : 
      {"message": `Не найден статус [${userRequest.statusID}]`}
    }
  };

  // ГАРД проверка можно ли переводить в указанный стаус из текущего
  // TODO отрефакторить в более простые условия
  if (!resultServicesStatusById[0].nextStatusID || (resultServicesStatusById[0].nextStatusID && resultServicesStatusById[0].nextStatusID.split(',').map(el=>Number(el)).indexOf(userRequest.statusID) === -1)) {
    return { 
      "error" : 
      {"message": `Перевод заявки из статуса [${resultServiceById[0].statusID}] в статус [${userRequest.statusID}] недоступен`}
    };
  };

  // ГАРД при переводе заявки в статус что-то не так поле собщение не может быть пустым
  if (userRequest.statusID === 1 && !userRequest.message) {
    return { 
      "error" : 
      {"message": `При переводе заявки в статус [1] сообщение не может быть пустым.`}
    }
  };

  // если все ГАРДы пройдены, апдейтим заявку в services
  let sqlUpdateServiceById, paramsUpdateServiceById, resultUpdateServiceById;
  sqlUpdateServiceById = 'UPDATE services SET statusID = ? WHERE id = ?';
  paramsUpdateServiceById = [userRequest.statusID, userRequest.id];
  resultUpdateServiceById = await require('./kernel-db.js')(sqlUpdateServiceById, paramsUpdateServiceById);
  console.log("Полная инфа о статусе указанной заявки", resultUpdateServiceById);

  // добавим события по заявке в services_events
  // сначала запишем изменение статуса
  let sqlCreateServiceEvent, paramsCreateServiceEvent, resultCreateServiceEvent;
  sqlCreateServiceEvent = `INSERT INTO services_events (serviceID, type, direction, statusID, message) VALUES (?,?,?,?,?)`;
  paramsCreateServiceEvent = [
    userRequest.id,
    'status',
    'out',
    userRequest.statusID,
    userRequest.message ? userRequest.message : "",
  ];
  resultCreateServiceEvent = await require('./kernel-db.js')(sqlCreateServiceEvent, paramsCreateServiceEvent);
  console.log ("resultCreateServiceEvent", resultCreateServiceEvent);
  // затем добавим событие чата если есть сообщение
  if(userRequest.message) {
    sqlCreateServiceEvent = `INSERT INTO services_events (serviceID, type, direction, statusID, message) VALUES (?,?,?,?,?)`;
    paramsCreateServiceEvent = [
      userRequest.id,
      'message',
      'out',
      userRequest.statusID,
      userRequest.message ? userRequest.message : "",
    ];
    resultCreateServiceEvent = await require('./kernel-db.js')(sqlCreateServiceEvent, paramsCreateServiceEvent);
    console.log ("resultCreateServiceEvent", resultCreateServiceEvent);
  }
  // при успешном инсерте mysql возвращает объект с полями, из него можно взять insertId
  // {
  //   fieldCount: 0,
  //   affectedRows: 1,
  //   insertId: 44,
  //   info: '',
  //   serverStatus: 2,
  //   warningStatus: 0
  // }

  result = "Изменили заявку по запросу: " + JSON.stringify(userRequest);

  return result;
};

// список категорий заявок
exports.getServicesCategories = async userRequest => {
  // userRequest = {...req.query, ...{globalUser: global.user}}
  console.log('inside getServicesCategories:', userRequest);
  
  // если в парамерах GET-запроса есть all то возвращаем весь массив категорий
  if (userRequest.all) {
    let sqlServicesCategories, paramsServicesCategories, resultServicesCategories;
    sqlServicesCategories = 'SELECT * FROM services_categories';
    paramsServicesCategories = [];
    resultServicesCategories = await require('./kernel-db.js')(sqlServicesCategories, paramsServicesCategories);
    console.log("Массив категорий", resultServicesCategories);
    return resultServicesCategories;
  };

  // если в параметрах GET-запроса присутсвтует id, то запрашиваем категории по нему, иначе только верхний уровень
  let sqlServicesCategories, paramsServicesCategories, resultServicesCategories;
  paramsServicesCategories = userRequest.id ? [userRequest.id] : [0];
  sqlServicesCategories = 'SELECT * FROM services_categories WHERE parentID = ?';
  resultServicesCategories = await require('./kernel-db.js')(sqlServicesCategories, paramsServicesCategories);
  console.log("Массив категорий", resultServicesCategories);

  // инициализируем массив разделов
  const serviceCategoryViews = [];

  // инициализируем и заполним раздел категорий
  const serviceCategoryView = {};
  serviceCategoryView.title = !userRequest.id ? "Где проблема?" : "Уточните проблему";
  serviceCategoryView.subtitle = !userRequest.id ? "Платные услуги?" : null;
  serviceCategoryView.description = !userRequest.id ? "Выберите подходящую категорию" : null;
  serviceCategoryView.menuType = !userRequest.id ? "big" : "small";
  
  // присвоим полю categories массив полученных в запросе категорий по parentID
  serviceCategoryView.categories = [];
  for (const [indexServicesCategories, rowServicesCategories] of resultServicesCategories.entries()) {
    // приведем поле iconUrl к полному виду
    if (resultServicesCategories[indexServicesCategories].iconUrl) {
      resultServicesCategories[indexServicesCategories].iconUrl = 
      // `http://127.0.0.1:${ENV.PortDevKit}/api/v2/assets/img/${resultServicesCategories[indexServicesCategories].iconUrl}`;
      `https://app.uk-kit.ru:51579/api/v2/assets/img/${resultServicesCategories[indexServicesCategories].iconUrl}`;
    };
  };
  serviceCategoryView.categories = resultServicesCategories;

  // добавим сформированыый раздел в массив разделов перед отдачей  на клиент
  serviceCategoryViews.push(serviceCategoryView);

  return serviceCategoryViews;

};


// АРМ - поиск постоянных пропусков
exports.getArmPasses = async userRequest => {
  // userRequest = { ...req.query, globalUser: global.user }
  console.log('inside getArmPasses:', userRequest);

  // ГАРД в параметрах запроса отсутсвует или пустой number 
  if (!userRequest.number) {
    return { 
      "error" : 
      {"message": `В запросе отсутсвует параметр number`}
    }
  };

  // return 'return from getArmPasses';

  // ГАРД number короче 3 символов
  const numberQueryMinLength = 3;
  if (userRequest.number.length < numberQueryMinLength) {
    return { 
      "error" : 
      {"message": `Параметр number должен содержать минимум ${numberQueryMinLength} символа`}
    }
  };

  // return 'return from getArmPasses';

  // запросим все номера с вхождением строки number
  let sqlGetArmPasses, paramsGetArmPasses, resultGetArmPasses;
  // sqlGetArmPasses = 'SELECT * FROM cars WHERE number LIKE ?';
  sqlGetArmPasses = `
    SELECT 
      cars.number AS "autoNumber",
      cars.idCvs AS "idCvs",
      users.tel AS "ownerTel",
      CONCAT (COALESCE(users.chosenSurName, ''), ' ', COALESCE(users.chosenName, ''), ' ', COALESCE(users.chosenMiddleName, '')) AS "ownerName",
      (SELECT GROUP_CONCAT(accounts.apptNumberFull ORDER BY accounts.apptNumberFull ASC SEPARATOR '; ') FROM accounts WHERE accounts.userID = cars.userID) AS "ownerAptNumber" 
    FROM cars LEFT JOIN users ON cars.userID = users.id 
    WHERE cars.number LIKE ?
    ORDER BY cars.userID ASC
    LIMIT 10`;
  paramsGetArmPasses = [`%${userRequest.number}%`];
  resultGetArmPasses = await require('./kernel-db.js')(sqlGetArmPasses, paramsGetArmPasses);
  console.log("Результат поиска постоянных пропусков для АРМ", resultGetArmPasses);

  // соберем модельку дя ответа на АРМ
  // for (const [indexGetArmPasses, rowGetArmPasses] of resultGetArmPasses.entries()) {
  //   resultGetArmPasses[indexGetArmPasses].issuer = {};
  //   resultGetArmPasses[indexGetArmPasses].issuer.number = resultGetArmPasses[indexGetArmPasses].ownerAptNumber;
  // };

  return resultGetArmPasses;

};


// АРМ - поиск постоянных пропусков
exports.getArmConfig = async userRequest => {
  // userRequest = undefined
  console.log('inside getArmConfig:', userRequest);

  return {
    'numberQueryMinLength': 3,
    'queryDelay': 300
  };

};


// отдача QR-кода для оплаты
exports.getQR = async userRequest => {
  // userRequest = {file, is_file_found, qr_image_base64}
  console.log('inside getQR:', userRequest.file, userRequest.is_file_found); // не логируем base64 строку png
  // логируем в бд
  let sqlLogRequest, paramsLogRequest, resultLogRequest;
  sqlLogRequest = `INSERT INTO dolgi_qr_access_log (
      bill_qr_name,
      is_file_found
    ) VALUES (?,?)`;
  
    paramsLogRequest = [
      userRequest.file,
      userRequest.is_file_found
    ];

  // sqlLogRequest = `INSERT INTO dolgi_qr_access_log (
  //     user_id, 
  //     user_tel, 
  //     user_fio, 
  //     user_type, 
  //     account_number, 
  //     account_apt_number, 
  //     bill_period, 
  //     bill_sum,
  //     bill_qr_name
  //   ) VALUES (?,?,?,?,?,?,?,?,?)`; 
	
  // paramsLogRequest = [
  //   user_id, 
  //   user_tel, 
  //   user_fio, 
  //   user_type, 
  //   account_number, 
  //   account_apt_number, 
  //   bill_period, 
  //   bill_sum,
  //   bill_qr_name
  // ];

  resultLogRequest = await require('./kernel-db.js')(sqlLogRequest, paramsLogRequest);
  console.log ("resultLogRequest", resultLogRequest);

  // получим лс по qr
  let sqlGetAccount, paramsGetAccount, resultGetAccount;
  sqlGetAccount = 'SELECT * FROM dolgi_qr WHERE name=?';
  paramsGetAccount = [userRequest.file];
  resultGetAccount = await require('./kernel-db.js')(sqlGetAccount, paramsGetAccount);
  console.log("ЛС привязанный к QR", resultGetAccount);

  // получим детали квитанции по лс
  let sqlBillDetails, paramsBillDetails, resultBillDetails;
  sqlBillDetails = 'SELECT * FROM dolgi_details WHERE account_number=?';
  paramsBillDetails = [resultGetAccount[0].account_number1];
  resultBillDetails = await require('./kernel-db.js')(sqlBillDetails, paramsBillDetails);
  console.log("Детали квитанции по лс: ", resultBillDetails);

  // получим ссылку на ЕПД
  let sqlGetEpd, paramsGetEpd, resultGetEpd;
  sqlGetEpd = 'SELECT * FROM dolgi_epd WHERE account_number1=?';
  paramsGetEpd = [resultGetAccount[0].account_number1];
  resultGetEpd = await require('./kernel-db.js')(sqlGetEpd, paramsGetEpd);
  console.log("ЕПД привязанный к ЛС: ", resultGetEpd);

  let html = `<!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Начисления по л/с ${resultGetAccount[0].account_number1}</title>
  </head>`;
  
  html += `<body>`;

  html += `<style>
  * {
    padding: 0;
    border: 0;
    margin: 0;
    font-family: Arial, Helvetica, sans-serif;
  }

  body {
    display: flex;
    flex-direction: column;
    padding: 20px;
    background-color: #fff;
  }

  h3 {
    text-align: center;
    padding: 10px;
  }

  .qr {
    text-align: center;
    border: 10px solid #a3cfcd;
    margin: 20px 0;
  }

  table {
    padding: 20px 0;
  }

  tr:nth-child(even) {
    background-color: #d3edec
  }

  th {
    background-color: #a3cfcd;
    padding: 5px;
    font-size: small;
  }

  td {
    font-family: Arial, Helvetica, sans-serif;
    vertical-align: top;
    font-size: small;
    padding: 5px;
  }

  .td_service {
    text-align: left;
    padding-right: 10%;
  }
  
  .td_sum {
    text-align: right;
  }

  .epd_link {
    text-align: center;
  }

  </style>
  `;
  
  html += `<h3>QR-код для оплаты через банковское приложение</h3>`;

  html += `<img class='qr' src="data:image/png;base64,${userRequest.qr_image_base64}">`;
  
  html += `<h3>Детализация по начислениям</h3>`;
  html += `<h3>Л/с № ${resultGetAccount[0].account_number1}</h3>`;

  html += `<table>`;
  
  html += `<thead>`;
  html += `<th class='td_service'>Услуга</th>`;
  html += `<th class='td_sum'>Начислено</th>`;
  html += `</thead>`;

  for (const [indexBillDetail, rowBillDetail] of resultBillDetails.entries()) {
    html += `
    <tr>
    <td class='td_service'>${rowBillDetail.service}</td>
    <td class='td_sum'>${formatMysqlDecimalToCur(rowBillDetail.end_period_sum)}</td>
    </tr>
    `;
  };

  html += `</table>`;

  if(resultGetEpd.length) {
    html += `<a class='epd_link' href="https://app.uk-kit.ru:51579/api/v2/assets/epd/${resultGetEpd[0].name}">Скачать полную квитанцию в формате PDF</a>`;
    // html += `<a class='epd_link' href="http://127.0.0.1:51569/api/v2/assets/epd/${resultGetEpd[0].name}">Скачать полную квитанцию в формате PDF</a>`;
  }

  html += `</body>`;
  html += `</html>`;
  
  console.log('html:', html);

  return html;

};


exports.postPaymentsCreate = async userRequest => {
  // userRequest = {...req.body, ...{globalUser: global.user}}
  console.log('inside postPaymentsCreate:', userRequest);
  let sql, params, result;

  // в запросе дб укажзан номер л/с из которого вызывается платеж

  // ГАРД - убедиться что юзер имеет право создавать платеж
  
  // создать ULID нового платежа 
  const order_id = require('ulid').ulid().toLowerCase();

  // и заинсертить его в табл транзакций, получить id новой записи
  const id = Math.floor(Math.random() * (100 - 10) + 10);

  // вернуть в ответе джсон с полями
  // id нового платежжа = ULID
  // editable (можно ли сумму редактировать)
  // title (Укажите сумму)
  // subtitle (Введите сумму и согласитесь с условиями сервиса)
  // terms_url (урл на условия)

  result = {
    id: id,
    order_id: order_id,
    account_id: userRequest.accountID,
    sum_editable: 1,
    prompt_title: "Укажите сумму",
    prompt_subtitle: "Введите сумму перевода и согласитесь с условиями сервиса",
    // terms_url: `https://app.uk-kit.ru:51579/api/v2/assets/doc/`
    terms_url: `https://uk-kit.ru`
  }
  
  // result = "Вернули полную модель пустого платежа для заполнения";

  return result;
};


exports.postPayments = async userRequest => {
  // userRequest = {...req.body, ...{globalUser: global.user}}
  console.log('inside postPayments:', userRequest);
  let sql, params, result;

  // в запросе указана 
  // id транзакции созданной на предыдущем шаге
  // сумма в копейках, который юзер соласился заплатить

  // в ответе 200 ОК
  // id нового платежжа = ULID
  // сумма с комиссией AFT
  // наименование платежа для подстановки в sdk -- (ФИО, номер л/с)
  // объект с чеком

  result = {
    id: userRequest.id,
    order_id: userRequest.order_id,
    payment_amount: userRequest.payment_amount,
    payment_amount_final: userRequest.payment_amount + 249,
    payment_description: `Платеж по л/с 123456, Иванов Иван Иванович`,
    receipt: {
      field1: "123",
      field2: "456"
    }
  }

  // result = "Создали новую транзакцию по ранее присланной модели";

  return result;
};


exports.patchPaymentsById = async userRequest => {
  // userRequest = {...req.body, ...{globalUser: global.user}}
  console.log('inside patchPaymentsById:', userRequest);
  let sql, params, result;

  // в запросе указана 
  // id транзакции созданной на предыдущем шаге
  // status с которым завершился sdk
  // description доп текстовое поле для описания статуса, запасное

  // в ответе 200 ОК
  // id нового платежжа = ULID

  result = {
    order_id: userRequest.order_id,
    payment_status: userRequest.payment_status
  }

  // result = "Обновили транзакцию с id " + userRequest;

  return result;
};


exports.getOperationsHistory = async userRequest => {
  // userRequest = {globalUser: global.user}
  console.log('inside getRoles:', userRequest);

  // ГАРД доп проверка на наличие id юзера в userRequest
  if (!userRequest.globalUser.id) return {
    "error" : {
      "message": `В запросе списка ролей отсутствует id пользователя globalUser`
    },
  };

  // в запросе токен авторизации, по нему вытаскиваем все платежи юзера
  // из доп параметров -- номер л/с


  // let sqlGetRoles, paramsGetRoles, resultGetRoles;
  // sqlGetRoles = 'SELECT * FROM access_roles WHERE ownerId = ? OR ownerId = ?';
  // paramsGetRoles = [0, userRequest.globalUser.id];
  // resultGetRoles = await require('./kernel-db.js')(sqlGetRoles, paramsGetRoles);
  // console.log("Массив ролей", resultGetRoles);

  result = {
    payments: [
      {
        "title": "Платеж по л/с 1112223334",
        "amount": "+ 16 790,50 ₽",
        "date": "12 марта"
      },
      {
        "title": "Платеж по л/с 1112223334",
        "amount": "+ 21 229,00 ₽",
        "date": "31 января"
      }
    ],
    bills: [
      {
        "title": "Начисление по л/с 1112223334",
        "amount": "- 9 403,81 ₽",
        "date": "12 марта"
      },
      {
        "title": "Начисление по л/с 1112223334",
        "amount": "- 11 117,13 ₽",
        "date": "22 февраля"
      },
      {
        "title": "Начисление по л/с 1112223334",
        "amount": "- 5 853,20 ₽",
        "date": "31 января"
      }
    ]
  };

  return result;
};