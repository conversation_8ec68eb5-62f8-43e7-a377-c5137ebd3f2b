"use strict";

const sendOrderSpec = async userRequest => {
  let userError = {
    messages: []
  };

  // вытащим имя текущего модуля без расширения
  const path = require("path");
  const moduleName = path.basename(__filename, path.extname(__filename));

  
  const orderID = "Д-" + [..."XXXX"].map(ch => Math.round(Math.random() * 9)).join("");

  // Подготовка и отправка сообщения в УК

  let messageText = "";
  let messageHTML = "";

  messageHTML += `<strong>Требуемые специалисты:</strong> ${userRequest.specialists.join(',')} <br /><br />`;
  messageHTML += `<strong>Текст сообщения:</strong> ${userRequest.description} <br />`;

  let requestParams = {
    clientEmail: userRequest.ucEmail,
    messageSubject: `Заявка ${orderID} в диспетчерскую от абонента, лицевой счет ${userRequest.clientAccount}`,
    messageText: messageText,
    messageHTML: messageHTML
  };

  const ucSendStatus = await require("./kernel.js")(requestParams, userError, "");
  if (!ucSendStatus) return null; // кернел вернул фолси - отдаем null выше по стеку на уровень БЛ

  // Подготовка и отправка сообщения абоненту

  // messageText = "";
  // messageHTML = "";

  // messageHTML += `<strong>Требуемые специалисты:</strong> ${userRequest.specialists.join(',')} <br /><br />`;
  // messageHTML += `<strong>Текст сообщения:</strong> ${userRequest.description} <br /><br />`;
  // messageHTML += `<strong>Лицевой счет:</strong> ${userRequest.clientAccount} <br />`;

  // requestParams = {
  //   clientEmail: userRequest.clientEmail,
  //   messageSubject: `Вашей заявке присвоен номер ${orderID}`,
  //   messageText: messageText,
  //   messageHTML: messageHTML
  // };

  // const clientSendStatus = await require("./kernel.js")(requestParams, userError, "");
  // if (!clientSendStatus) return null; // кернел вернул фолси - отдаем null выше по стеку на уровень БЛ

  return { ...ucSendStatus, ...{ sendOrderSpec: "OK", orderID: orderID } };
};

module.exports = sendOrderSpec;