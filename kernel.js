"use strict";

const kernel = async (requestParams, userError, APImethod) => {
  // проверка userError -> выход
  // if (userError.messages.length !== 0) {
  //   console.log("Запрос не отправлен, обнаружены ошибки");
  //   console.dir(userError.messages);
  //   return null; // возвращаем из кернела null - ошибка
  // }

  let transporter = require("nodemailer").createTransport({
    host: "smtp.mail.ru",
    port: 465,
    secure: true, // upgrade later with STARTTLS
    auth: {
      user: "watermeter-info",
      pass: "1rGHVneQBs77eA0r6Pjf"
      // pass: "y011235813"
    }
  });

  // verify connection configuration
  // если ошибка вылетает тут - надо проверить конфигурацию smtp
  // пока его запускать не нужно
  transporter.verify(function (error, success) {
    // if (error) {
    //   console.log(error);
    // } else {
    //   console.log("Server is ready to take our messages");
    // }
  });

  const receiver = requestParams.clientEmail;

  // const receiver = "<EMAIL>";
  // const receiver = "<EMAIL>";
  // const receiver = "<EMAIL>";
  // const receiver = "<EMAIL>";
  // const receiver = "<EMAIL>";
  // bcc: "<EMAIL>",

  const message = {
    from: "Счетчик воды <<EMAIL>>",
    to: receiver,
    bcc: "<EMAIL>",
    subject: requestParams.messageSubject,
    // text: "Plaintext version of the message",
    text: requestParams.messageText,
    // html: "<p>HTML version of the message</p>"
    html: requestParams.messageHTML
  };

  const cb = (err, info) => {
    console.log(err.response);
    console.log(info);
  };

  transporter.sendMail(message, cb);

  return { kernel: "OK" };
};

module.exports = kernel;
