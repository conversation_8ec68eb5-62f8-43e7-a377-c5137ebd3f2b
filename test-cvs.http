@cvs_host_full = http://*************:5080
@cvs_content_type = application/json
@cvs_authorization = Basic YWtsdXgxOjAxMTIzNTgxMw==

###

GET {{cvs_host_full}}/camera
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/units
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp?active=0&datefrom=20230208T000000Z
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp?plate=m434oa797
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

### по externalId возвращает все
GET {{cvs_host_full}}/reglp?externalId=1017
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/reglp/16615
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/groups
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/lp/latest
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

GET {{cvs_host_full}}/lp?datefrom=20230202T123854Z
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

POST {{cvs_host_full}}/reglp
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

{
    "plate": "Y000YY222",
    "expireDateTime": "20240116T063758Z",
    "groupId": 3,
    "externalId": 100411
}

###

GET {{cvs_host_full}}/reglp?plate=M311BO50
# GET {{cvs_host_full}}/reglp?description=~2000746~
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###

PUT {{cvs_host_full}}/reglp
content-type: {{cvs_content_type}}image.png
Authorization: {{cvs_authorization}}

{   
    "id": 122059,
    "plate": "M311BO50",
    "groupId": 3,
    "description": "** ПП: Кв 800; Мм 448; Мм 449; Тел: 79250546033; [2000746]",
    "externalId": 3414,
    "active": 1
}

# при апдейте пропуска необходимо заново отправлять поля plate, description, externalId иначе обнулятся

###

DELETE {{cvs_host_full}}/reglp/4497
content-type: {{cvs_content_type}}
Authorization: {{cvs_authorization}}

###
