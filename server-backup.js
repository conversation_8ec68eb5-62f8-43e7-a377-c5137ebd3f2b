"use strict";

const express = require("express");
const app = express();

// TODO сделать так чтобы порт определялся из env
const PORT = 51557;

// https://stackoverflow.com/a/33419220
var fs = require("fs");
var logger = fs.createWriteStream("log.txt", {
  flags: "a" // 'a' means appending (old data will be preserved)
});

app.use(express.json());

app.use(setHeaders); // ставим все хедеры как application/json
app.use(bodyParamsCheck); // проверяем ошибки в body запроса

// middlware - обработчик ошибок, последний в очереди мидлварей
// https://www.youtube.com/watch?v=UVAMha41dwo
app.use((error, req, res, next) => {
  console.log("********************* app.use((error, req, res, next)"); // времлог
  const status = error.status || 500;
  const errObj = {
    state: status,
    error: error.message
  };
  res.status(status).json(errObj).end();
  log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
});

// по умолчанию любой отправляемый ответ будет application/json
function setHeaders(req, res, next) {
  res.setHeader("Content-Type", "application/json");
  next();
}

// проверяем обязательные поля
function bodyParamsCheck(req, res, next) {
  console.log("********************* function bodyParamsCheck(req, res, next)"); // времлог
  if (req.body.apikey !== "MqVaGfk*97nVBy_C") {
    const error = new Error("Incorrect apikey");
    error.status = 400;
    // перенес сюда этот блок из catch(error)
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    // конец блока из catch(error)
    next(error);
  }
  if (!req.body.data) {
    const error = new Error("No 'data' property");
    error.status = 400;
    // перенес сюда этот блок из catch(error)
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    // конец блока из catch(error)
    next(error);
  }
  next();
}

function log(str, req) {
  const d = new Date();
  const nowDate = d.getFullYear() + "/" + d.getMonth() + "/" + d.getDate();
  const nowTime = d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
  const now = nowDate + " " + nowTime;
  const ip = req.ip.toString().replace("::ffff:", ""); // https://stackoverflow.com/a/54263403
  logger.write("[" + ip + "] " + now + "\r\n" + str + "\r\n\r\n");
}

// универсальный обработчик респонса всех хендлеров в блоке switch-case
function handleResponse(handlerResponse, req, res, method) {
  if (handlerResponse.errorMsg) {
    const responseJSON = { state: 400, error: handlerResponse.errorMsg, method: method };
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(responseJSON), req);
    res.status(400).json(responseJSON).end();
  } else {
    const responseJSON = { state: 200, method: method };
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(responseJSON), req);
    res.status(200).json(responseJSON).end();
  }
}

app.post("/api/v1", async (req, res) => {
  try {
    // TODO доделать обработку runtime-ошибок внутри app.post чтобы stack-trace c путями не вываливался клиенту
    // TODO тк try-catch не помогает
    // if (err.message) return false;

    console.log(new Date());
    console.log("==== REQUEST HEADERS");
    console.dir(req.headers);
    console.log("==== BODY");
    console.dir(req.body);

    const body = req.body;
    const data = body.data;
    let response = {};
    let result = {};

    switch (body.method) {
      case "test.api":
        console.log("********************* case test.api:"); // времлог
        // тест: возвращаем зеркально джсон body запроса
        response = { state: 200, method: body.method };
        res.status(200).json(response).end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "verifyEmail":
        console.log("********************* case verifyEmail:"); // времлог

        // проверка поля clientMail, если falsy кинуть 400
        if (!req.body.data.clientEmail) {
          const error = new Error("Отсутствие емейла клиента в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок

        result = await require("./sendVerificationCode.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendMeterValues":
        console.log("********************* case sendMeterValues:"); // времлог
        
        // проверка полей с емейлами - если одного из них нет, то кинуть 400
        if (!req.body.data.clientEmail || !req.body.data.ucEmail) {
          const error = new Error("Отсутствие одного из емейлов в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendMeterValues.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();

        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendOrderSpec":
        console.log("********************* case sendOrderSpec:");

        // проверка поля ucMail, если falsy кинуть 400
        if (!req.body.data.ucEmail) {
          const error = new Error("Отсутствие емейла УК в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendOrderSpec.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendOrderPass":
        console.log("********************* case sendOrderPass:");

        // проверка поля ucMail, если falsy кинуть 400
        if (!req.body.data.ucEmail) {
          const error = new Error("Отсутствие емейла УК в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendOrderPass.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "sendMessage":
        console.log("********************* case sendMessage:");

        // проверка полей
        if (!req.body.data.ucEmail) {
          const error = new Error("Отсутствие емейла УК в запросе");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./sendMessage.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "updateDeviceToken":
        console.log("********************* case updateDeviceToken:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.deviceid || !req.body.data.token) {
          const error = new Error("Отсутствие deviceid или token");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./updateDeviceToken.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
          
      case "sendPushToAll":
        console.log("********************* case sendPushToAll:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.message || !req.body.data.title) {
          const error = new Error("Отсутствие message или title");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        // result = await require("./sendPushToAll.js")(body.data); // временно вырубил мало ли что
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "getNews":
        console.log("********************* case getNews:");

        // проверка полей кинуть 400
        // if (!req.body.data.message || !req.body.data.title) {
        //   const error = new Error("Отсутствие message или title");
        //   error.status = 400;
        //   const errObj = {
        //     state: error.status,
        //     error: error.message
        //   };
        //   res.status(error.status).json(errObj).end();
        //   console.log("Ошибка");
        //   console.log(errObj);
        //   return;
        // };

        // проверка прошла, основной блок
        result = await require("./getNews.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      case "getNewsArticle":
        console.log("********************* case getNewsArticle:");

        // проверка поля
        if (!req.body.data || !req.body.data.id) {
          const error = new Error("Отсутствие data или id");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        result = await require("./getNewsArticle.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...result })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
  
      case "getVerificationCode":
        console.log("********************* case getVerificationCode:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.tel) {
          const error = new Error("Отсутсвует или пустое поле data.tel");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        if (!"79160271831,79263120274".includes(req.body.data.tel) ) {
          const error = new Error("Номер телефона не зарегистрирован в реестре квартиросъемщиков.");
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        // result = await require("./sendPushToAll.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...{ tel: req.body.data.tel, timer: "90"} })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;
    
      case "verifyCode":
        console.log("********************* case verifyCode:");

        // проверка полей, если falsy кинуть 400
        if (!req.body.data.tel  || !req.body.data.code) {
          const error = new Error("Отсутсвует или пустое поле data.tel или data.code");
          error.status = 400;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        if (!"79160271831,79263120274".includes(req.body.data.tel) ) {
          const error = new Error("Номер телефона не запрашивал кода авторизации.");
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        if (req.body.data.code !== '1111' ) {
          const error = new Error("Неверный код верификации");
          error.status = 422;
          const errObj = {
            state: error.status,
            error: error.message
          };
          res.status(error.status).json(errObj).end();
          console.log("Ошибка");
          console.log(errObj);
          return;
        };

        // проверка прошла, основной блок
        // result = await require("./sendPushToAll.js")(body.data);
        res
          .status(200)
          .json({ ...{ state: 200 }, ...{ tel: req.body.data.tel, authToken: "123456789asdfghjkl"} })
          .end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
      break;

      default:
        console.log("********************* case default:");
        response = { state: 400, error: "Метод API не распознан" };
        res.status(400).json(response).end();
        log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: " + JSON.stringify(response), req);
    }
  } catch (error) {
    console.log("********************* catch (error)"); // времлог
    console.log(error);
    const errObj = {
      state: error.status,
      error: error.message
    };
    res.status(error.status).json(errObj).end();
    log("Request: " + JSON.stringify(req.body) + "\r\n" + "Response: error " + JSON.stringify(errObj), req);
    return false;
  }
});

app.listen(PORT, err => {
  if (err) {
    return console.log("ОШИБКА");
    // return console.log(err);
  }
  console.log("listening on port", PORT);
});
